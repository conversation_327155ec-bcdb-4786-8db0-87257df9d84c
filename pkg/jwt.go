package pkg

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"errors"
	"strings"
	"time"
)

type Jwt struct{}

type JwtPayload struct {
	PayloadAuth PayloadAuth
	Hash        string
}

type PayloadAuth struct {
	Issuer     string  `json:"iss"`
	Parent     *string `json:"par,omitempty"`
	Pro        string  `json:"pro"`
	User       string  `json:"usr"`
	Lid        string  `json:"lid"`
	Role       int     `json:"rol"`
	Sub        string  `json:"sub"`
	Version    int     `json:"ver"`
	Expiration int64   `json:"exp"`
	IssuedAt   int64   `json:"iat"`
	Telegram   *bool   `json:"tel,omitempty"`
	Signature  *string `json:"-"`
}

type Result struct {
	Jwt    string
	Secret string
}

// Create JWT token with PayloadAuth struct
func (j *Jwt) GenerateToken(payload PayloadAuth, secret string) (*Result, error) {
	// Create token header
	header := map[string]string{
		"typ": "JWT",
		"alg": "HS256",
	}

	// Encode Header to Base64Url
	headerJson, err := json.Marshal(header)
	if err != nil {
		return nil, err
	}
	base64UrlHeader := base64.RawURLEncoding.EncodeToString(headerJson)

	// Encode Payload to Base64Url
	payloadJson, err := json.Marshal(payload)
	if err != nil {
		return nil, err
	}
	base64UrlPayload := base64.RawURLEncoding.EncodeToString(payloadJson)

	// Create Signature
	signature := createSignature(base64UrlHeader+"."+base64UrlPayload, secret)

	// Combine header, payload, and signature to form the JWT
	token := base64UrlHeader + "." + base64UrlPayload + "." + signature

	return &Result{
		Jwt:    token,
		Secret: signature,
	}, nil
}

// Get and Decode JWT Token
func (j *Jwt) GetJwt(token string) (*JwtPayload, error) {
	parts := strings.Split(token, ".")
	if len(parts) != 3 {
		return nil, errors.New("invalid token format")
	}

	// Decode Payload
	payloadDecoded, err := base64.RawURLEncoding.DecodeString(parts[1])
	if err != nil {
		return nil, err
	}

	var payloadAuth PayloadAuth
	if err := json.Unmarshal(payloadDecoded, &payloadAuth); err != nil {
		return nil, err
	}

	signatureProvided := parts[2]

	return &JwtPayload{
		PayloadAuth: payloadAuth,
		Hash:        signatureProvided,
	}, nil
}

// Validate the JWT token signature and expiration
func (j *Jwt) ValidateToken(token string, secret string) (bool, error) {
	parts := strings.Split(token, ".")
	if len(parts) != 3 {
		return false, errors.New("invalid token format")
	}

	// Rebuild the signature using header and payload
	expectedSignature := createSignature(parts[0]+"."+parts[1], secret)

	// Check if the provided signature matches
	if parts[2] != expectedSignature {
		return false, errors.New("signature is invalid")
	}

	// Decode the payload and check expiration
	payloadDecoded, err := base64.RawURLEncoding.DecodeString(parts[1])
	if err != nil {
		return false, err
	}

	var payloadAuth PayloadAuth
	if err := json.Unmarshal(payloadDecoded, &payloadAuth); err != nil {
		return false, err
	}

	if payloadAuth.Expiration < time.Now().Unix() {
		return false, errors.New("token is expired")
	}

	return true, nil
}

// Helper function to create HMAC SHA-256 signature
func createSignature(data string, secret string) string {
	h := hmac.New(sha256.New, []byte(secret))
	h.Write([]byte(data))
	signature := h.Sum(nil)
	return base64.RawURLEncoding.EncodeToString(signature)
}
