package pkg

import (
	"crypto/hmac"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"regexp"
	"strings"
	"time"

	"golang.org/x/crypto/nacl/secretbox"
)

const ObjectIDRegex string = `[0-9a-fA-F]{24}`
const UUIDRegEx string = `[0-9a-fA-F]{8}\-[0-9a-fA-F]{4}\-[0-9a-fA-F]{4}\-[0-9a-fA-F]{4}\-[0-9a-fA-F]{12}`
const HexRegex string = `[0-9a-fA-F]{32}`

func SetBoolPointer(input *bool, action string) *bool {
	if input != nil {
		return input
	}
	if action == "create" {
		b := false
		return &b
	}
	return nil
}

func GetBoolValue(input *bool, defaultValue bool) bool {
	if input != nil {
		return *input
	}
	return defaultValue
}

func SetInt8Pointer(input *int8, action string) *int8 {
	if input != nil {
		return input
	}

	if action == "create" {
		b := int8(0)
		return &b
	}

	return nil
}

func SetInt64Pointer(input *int64, action string) *int64 {
	if input != nil {
		return input
	}

	if action == "create" {
		b := int64(0)
		return &b
	}

	return nil
}

func SetIntPointer(input *int, action string) *int {
	if input != nil {
		return input
	}

	if action == "create" {
		b := int(0)
		return &b
	}

	return nil
}

func GetInt8Value(input *int8, defaultValue int8) int8 {
	if input != nil {
		return *input
	}
	return defaultValue
}

func GetIntValue(input *int, defaultValue int) int {
	if input != nil {
		return *input
	}
	return defaultValue
}

func GetInt64Value(input *int64, defaultValue int64) int64 {
	if input != nil {
		return *input
	}
	return defaultValue
}

func GetFloat64Value(input *float64, defaultValue float64) float64 {
	if input != nil {
		return *input
	}
	return defaultValue
}

func SetStringPointer(input *string, action string) *string {
	if input != nil {
		return input
	}
	if action == "create" {
		b := ""
		return &b
	}
	return nil
}

func GetStringValue(input *string, defaultValue string) string {
	if input != nil {
		return *input
	}
	return defaultValue
}

func SetStringUpperPointer(input *string, action string) *string {
	if input != nil {
		str := *input // Dereference the input pointer
		upperStr := strings.ToUpper(str)
		return &upperStr
	}
	if action == "create" {
		b := ""
		return &b
	}
	return nil
}

const DateLayoutDefault = "2006-01-02 15:04:05"

func ParseDateString(dateStr, layout string) (time.Time, error) {
	parsedTime, err := time.Parse(layout, dateStr)
	if err != nil {
		return time.Time{}, fmt.Errorf("failed to parse date string: %w", err)
	}
	return parsedTime.UTC(), nil
}

func GenerateRandomState(n int) (string, error) {
	// Create a byte slice with length n
	b := make([]byte, n)
	_, err := rand.Read(b)
	if err != nil {
		return "", err
	}
	// Encode the byte slice into a base64 string
	return base64.URLEncoding.EncodeToString(b), nil
}

func GetCurrentDateTime() string {
	// Use the Go reference layout to format the current time
	return time.Now().Format(DateLayoutDefault)
}

func Strtotime(dateString string) (time.Time, error) {
	parsedTime, err := time.Parse(DateLayoutDefault, dateString)
	if err != nil {
		return time.Time{}, err
	}
	return parsedTime, nil
}

func StrtotimeToTimestamp(dateString string) (int64, error) {
	parsedTime, err := time.Parse(DateLayoutDefault, dateString)
	if err != nil {
		return 0, err
	}
	return parsedTime.Unix(), nil
}

func GetValidUntil() time.Time {
	// Get the current time
	currentTime := time.Now()

	// Add one year to the current time
	validUntil := currentTime.AddDate(1, 0, 0) // Add 1 year, 0 months, 0 days

	// Return Unix timestamp of the validUntil time
	return validUntil
}

func SetTemporaryExpiredTime() string {
	now := time.Now()
	futureDate := now.Add(time.Duration(5) * time.Minute)
	return futureDate.Format(DateLayoutDefault)
}

func SetExpireInYears(years int) string {
	now := time.Now()
	futureDate := now.AddDate(years, 0, 0)
	return futureDate.Format(DateLayoutDefault)
}

func SetExpireInDays(days int) string {
	now := time.Now()
	futureDate := now.AddDate(0, 0, days)
	return futureDate.Format(DateLayoutDefault)
}

func GetIP(r *http.Request) string {
	var ip string

	if ip = r.Header.Get("CF-Connecting-IP"); ip != "" {
		return sanitizeIP(ip)
	}

	if ip = os.Getenv("HTTP_CLIENT_IP"); ip != "" && !strings.EqualFold(ip, "unknown") {
		return ip
	}

	if ip = os.Getenv("HTTP_X_FORWARDED_FOR"); ip != "" && !strings.EqualFold(ip, "unknown") {
		return ip
	}

	if ip = os.Getenv("REMOTE_ADDR"); ip != "" && !strings.EqualFold(ip, "unknown") {
		return ip
	}

	if ip = r.RemoteAddr; ip != "" && !strings.EqualFold(ip, "unknown") {
		return sanitizeIP(ip)
	}

	return "unknown"
}

func sanitizeIP(ip string) string {
	// sanitize the IP address (if needed)
	// strip_tags is not relevant in Go, but you may sanitize it according to your needs
	return strings.TrimSpace(ip)
}

// EncryptPassword encrypts the input string using AES encryption
func EncryptPassword(password string, key []byte) (string, error) {
	// Key must be 32 bytes long for secretbox
	if len(key) != 32 {
		return "", fmt.Errorf("key length must be 32 bytes")
	}

	// Create a new nonce for this encryption
	var nonce [24]byte
	if _, err := io.ReadFull(rand.Reader, nonce[:]); err != nil {
		return "", err
	}

	// Encrypt the password using secretbox
	encrypted := secretbox.Seal(nonce[:], []byte(password), &nonce, (*[32]byte)(key))

	// Return base64 encoded encrypted password
	return base64.StdEncoding.EncodeToString(encrypted), nil
}

// DecryptPassword decrypts the input string using AES decryption
func DecryptPassword(encryptedPassword string, key []byte) (string, error) {
	// Key must be 32 bytes long for secretbox
	if len(key) != 32 {
		return "", fmt.Errorf("key length must be 32 bytes")
	}

	// Decode the base64 encoded string
	encryptedData, err := base64.StdEncoding.DecodeString(encryptedPassword)
	if err != nil {
		return "", fmt.Errorf("failed to decode base64: %v", err)
	}

	// Check if the encrypted data is at least 24 bytes for the nonce
	if len(encryptedData) < 24 {
		return "", fmt.Errorf("encrypted data is too short")
	}

	// Extract the nonce (first 24 bytes)
	var nonce [24]byte
	copy(nonce[:], encryptedData[:24])

	// Decrypt the password using secretbox
	decrypted, ok := secretbox.Open(nil, encryptedData[24:], &nonce, (*[32]byte)(key))
	if !ok {
		return "", fmt.Errorf("decryption failed")
	}

	return string(decrypted), nil
}

func FilterAndUppercase(input *string) *string {

	if input == nil {
		return nil
	}

	re := regexp.MustCompile(`[^a-zA-Z0-9\.\s]`)

	cleaned := re.ReplaceAllString(*input, "")

	uppercased := strings.ToUpper(cleaned)

	return &uppercased
}

func FilterAndUppercaseAlias(alias *string) *string {

	if alias == nil {
		return nil
	}

	re := regexp.MustCompile(`[^a-zA-Z0-9]`)

	cleaned := re.ReplaceAllString(*alias, "")

	uppercased := strings.ToUpper(cleaned)

	return &uppercased
}

func FilterUsername(username *string) *string {
	if username == nil {
		return nil
	}

	re := regexp.MustCompile(`[^a-zA-Z0-9@]`)

	cleaned := re.ReplaceAllString(*username, "")

	uppercased := strings.ToUpper(cleaned)

	return &uppercased
}

func FilterPin(pin *string) *string {

	if pin == nil {
		return nil
	}

	re := regexp.MustCompile(`[^0-9]`)

	cleaned := re.ReplaceAllString(*pin, "")

	return &cleaned
}

func FilterIP(ip *string) *string {

	if ip == nil {
		return nil
	}

	uppercasedIP := strings.ToUpper(*ip)

	re := regexp.MustCompile(`[^0-9\.\,]`)

	cleanedIP := re.ReplaceAllString(uppercasedIP, "")

	return &cleanedIP
}

func FilterWhitelistIP(whitelistIP *string) *string {

	if whitelistIP == nil {
		return nil
	}

	uppercasedIP := strings.ToUpper(*whitelistIP)

	re := regexp.MustCompile(`[^0-9\.\,]`)

	cleanedIP := re.ReplaceAllString(uppercasedIP, "")

	return &cleanedIP
}

func FilterMobilePhoneCountry(country *string) *string {

	if country == nil {
		return nil
	}

	re := regexp.MustCompile(`[^a-zA-Z]`)
	filteredCountry := re.ReplaceAllString(*country, "")

	loweredCountry := strings.ToLower(filteredCountry)

	return &loweredCountry
}

func FilterMobilePhone(phone, phoneCode *string) *string {
	if phone == nil || phoneCode == nil {
		return nil
	}

	// Use regex to remove any characters except digits and the '+'
	re := regexp.MustCompile(`[^0-9\+]`)
	filteredPhone := re.ReplaceAllString(*phone, "")

	// Remove leading zero if it exists
	filteredPhone = strings.TrimPrefix(filteredPhone, "0")

	return &filteredPhone
}

func FilterMobilePhoneNumber(phoneCode, phone *string) *string {
	if phone == nil || phoneCode == nil {
		return nil
	}

	mobilePhoneNumber := *phoneCode + *phone
	return &mobilePhoneNumber
}

func GenerateHash(secretKey, strToHash string) string {
	h := hmac.New(sha256.New, []byte(secretKey))
	h.Write([]byte(strToHash))
	return hex.EncodeToString(h.Sum(nil))
}

func ConvertObjectToMap(o interface{}) (map[string]interface{}, error) {
	// Convert QueryObject to JSON
	jsonData, err := json.Marshal(o)
	if err != nil {
		return nil, err
	}

	// Unmarshal JSON to map
	var data map[string]interface{}
	err = json.Unmarshal(jsonData, &data)
	if err != nil {
		return nil, err
	}

	return data, nil
}
