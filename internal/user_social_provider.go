package internal

import (
	"fmt"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/pkg/util"
	validation "github.com/go-ozzo/ozzo-validation/v4"
)

type DataProfileAuth struct {
	Profile         *User                 `json:"profile,omitempty"`
	Session         *Session              `json:"session,omitempty"`
	SocialProviders *[]UserSocialProvider `json:"social_providers,omitempty"`
	Url             *string               `json:"url,omitempty"`
}

type UserSocialProvider struct {
	ID          string     `json:"_id"`
	User        string     `json:"user"`
	Parent      string     `json:"parent_id"`
	LoginID     string     `json:"login_id"`
	Alias       string     `json:"alias"`
	Provider    string     `json:"provider"`
	Email       string     `json:"email"`
	Name        string     `json:"name"`
	FirstName   string     `json:"firstname"`
	LastName    string     `json:"lastname"`
	NickName    string     `json:"nickname"`
	Description string     `json:"description"`
	UserID      string     `json:"user_id"`
	AvatarURL   string     `json:"avatar_url"`
	Location    string     `json:"location"`
	WhitelistIP string     `json:"whitelist_ip"`
	CreatedIP   string     `json:"created_ip"`
	CreatedAt   *time.Time `json:"created_at"`
	UpdatedAt   *time.Time `json:"updated_at"`
}

type UserSocialProviderPagin struct {
	Limit        int                  `json:"limit"`
	Page         int                  `json:"page"`
	Sort         string               `json:"sort"`
	TotalRecords int                  `json:"total_records"`
	TotalPages   int                  `json:"total_pages"`
	Records      []UserSocialProvider `json:"records"`
}

func (f UserSocialProvider) IsExist() bool {
	return f.ID != ""
}

func (f UserSocialProvider) AlreadyConnected(userId string) error {
	if f.User != userId {
		return validation.Errors{
			util.GetStructName(f): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already connected with another user.", f.NickName)),
		}
	}
	return nil
}

func (f UserSocialProvider) ValidateExist() error {
	if f.ID != "" {
		return validation.Errors{
			util.GetStructName(f): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already taken.", f.Name)),
		}
	}
	return nil
}

func (f UserSocialProvider) ValidateNotExist() error {
	if f.ID == "" {
		return validation.Errors{
			util.GetStructName(f): riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", f.ID)),
		}
	}
	return nil
}

func (f *UserSocialProvider) ResponseData() interface{} {
	if f.ID == "" {
		return map[string]interface{}{}
	} else {
		return f
	}
}

type CreateUserSocialProvider struct {
	User        string  `json:"user,omitempty"`
	Parent      string  `json:"parent,omitempty"`
	LoginID     *string `json:"login_id,omitempty"`
	Alias       *string `json:"alias,omitempty"`
	Provider    *string `json:"provider,omitempty"`
	Email       *string `json:"email,omitempty"`
	Name        *string `json:"name,omitempty"`
	FirstName   *string `json:"firstname,omitempty"`
	LastName    *string `json:"lastname,omitempty"`
	NickName    *string `json:"nickname,omitempty"`
	Description *string `json:"description,omitempty"`
	UserID      *string `json:"user_id,omitempty"`
	AvatarURL   *string `json:"avatar_url,omitempty"`
	Location    *string `json:"location,omitempty"`
	WhitelistIP *string `json:"whitelist_ip,omitempty"`
	CreatedIP   *string `json:"created_ip,omitempty"`
}

func (c CreateUserSocialProvider) Validate() error {
	if err := validation.ValidateStruct(&c,
		validation.Field(&c.User, validation.Required),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}

type QueryUserSocialProvider struct {
	Name string `json:"name,omitempty"`
}
