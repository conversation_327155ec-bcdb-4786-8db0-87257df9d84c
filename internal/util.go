package internal

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/continue-team/hecarim/pkg"
	"github.com/continue-team/riot"
)

type ResultGenerateTokenWebservice struct {
	Jwt    string
	Secret string
}

type Profile struct {
	ID        string `json:"_id"`
	ParentID  string `json:"parent_id"`
	Username  string `json:"username"`
	LoginID   string `json:"login_id"`
	Role      int    `json:"role"`
	CreatedIP string `json:"created_ip"`
}

type CreateTokenWebservice struct {
	WebserviceID string
	Util         riot.Util
	Profile      Profile
	IsTelegram   *bool
	ActionDate   string
	ValidUntil   string
}

func (ctw CreateTokenWebservice) GenerateToken() (*ResultGenerateTokenWebservice, error) {
	// Create token header as a JSON string
	header := map[string]string{
		"typ": "JWT",
		"alg": "HS256",
	}
	headerJSON, err := json.Marshal(header)
	if err != nil {
		return nil, err
	}

	actionDate, err := pkg.ParseDateString(ctw.ActionDate, pkg.DateLayoutDefault)
	if err != nil {
		return nil, err
	}
	// Create token payload as a JSON string
	validUntil, err := pkg.ParseDateString(ctw.ValidUntil, pkg.DateLayoutDefault)
	if err != nil {
		return nil, err
	}

	payload := map[string]interface{}{
		"iss": "CT",
		"pro": ctw.Profile.ID,
		"usr": ctw.Profile.Username,
		"lid": ctw.Profile.LoginID,
		"rol": ctw.Profile.Role,
		"sub": ctw.WebserviceID,
		"tel": ctw.IsTelegram,
		"ver": 5,
		"exp": validUntil.Unix(),
		"iat": actionDate.Unix(),
	}
	payloadJSON, err := json.Marshal(payload)
	if err != nil {
		return nil, err
	}

	base64UrlHeader := base64.RawURLEncoding.EncodeToString(headerJSON)

	base64UrlPayload := base64.RawURLEncoding.EncodeToString(payloadJSON)

	h := hmac.New(sha256.New, []byte(ctw.Util.Conf.Get("JWT_SECRET")))
	h.Write([]byte(fmt.Sprintf("%s.%s", base64UrlHeader, base64UrlPayload)))
	signature := h.Sum(nil)

	// Encode Signature to Base64Url String
	base64UrlSignature := base64.RawURLEncoding.EncodeToString(signature)

	// Create JWT
	jwt := fmt.Sprintf("%s.%s.%s", base64UrlHeader, base64UrlPayload, base64UrlSignature)

	return &ResultGenerateTokenWebservice{
		Jwt:    jwt,
		Secret: base64UrlSignature,
	}, nil
}

type ResultGenerateTokenSession struct {
	Jwt string
}

type CreateTokenSession struct {
	Util       riot.Util
	Profile    Profile
	IsGuest    bool
	IsBot      bool
	ActionDate string
	ValidUntil string
}

func (cts CreateTokenSession) GenerateToken() (*ResultGenerateTokenSession, error) {
	// Create token header as a JSON string
	header := map[string]string{
		"typ": "JWT",
		"alg": "HS256",
	}
	headerJSON, err := json.Marshal(header)
	if err != nil {
		return nil, err
	}

	actionDate, err := pkg.ParseDateString(cts.ActionDate, pkg.DateLayoutDefault)
	if err != nil {
		return nil, err
	}
	// Create token payload as a JSON string
	validUntil, err := pkg.ParseDateString(cts.ValidUntil, pkg.DateLayoutDefault)
	if err != nil {
		return nil, err
	}

	payload := map[string]interface{}{
		"iss": "CT",
		"par": cts.Profile.ParentID,
		"pro": cts.Profile.ID,
		"usr": cts.Profile.Username,
		"lid": cts.Profile.LoginID,
		"rol": cts.Profile.Role,
		"sub": cts.Profile.ID,
		"gue": cts.IsGuest,
		"bot": cts.IsBot,
		"ver": 5,
		"exp": validUntil.Unix(),
		"iat": actionDate.Unix(),
	}
	payloadJSON, err := json.Marshal(payload)
	if err != nil {
		return nil, err
	}

	base64UrlHeader := base64.RawURLEncoding.EncodeToString(headerJSON)

	base64UrlPayload := base64.RawURLEncoding.EncodeToString(payloadJSON)

	h := hmac.New(sha256.New, []byte(cts.Util.Conf.Get("JWT_SECRET")))
	h.Write([]byte(fmt.Sprintf("%s.%s", base64UrlHeader, base64UrlPayload)))
	signature := h.Sum(nil)

	// Encode Signature to Base64Url String
	base64UrlSignature := base64.RawURLEncoding.EncodeToString(signature)

	// Create JWT
	jwt := fmt.Sprintf("%s.%s.%s", base64UrlHeader, base64UrlPayload, base64UrlSignature)

	return &ResultGenerateTokenSession{
		Jwt: jwt,
	}, nil
}

func appendToHash(builder *strings.Builder, value string, firstElement *bool) {
	if !*firstElement {
		builder.WriteString(".")
	}
	builder.WriteString(value)
	*firstElement = false
}

type QueryObject struct {
	Username   string `json:"username,omitempty"`
	Session    string `json:"session,omitempty"`
	Pagination struct {
		Page  int `json:"page,omitempty"`
		Limit int `json:"limit,omitempty"`
	} `json:"pagination,omitempty"`
	Query struct {
		Keyword string `json:"keyword,omitempty"`
		Name    string `json:"name,omitempty"`
	} `json:"query,omitempty"`
	Hash   string `json:"hash,omitempty"`
	ByPass string `json:"bypass,omitempty"`
}

func (q QueryObject) ToQueries(secret string) (map[string][]string, error) {
	var strToHash string
	firstElement := true

	data, err := pkg.ConvertObjectToMap(q)
	if err != nil {
		return nil, err
	}

	// Convert map to queries map with string slices
	queries := make(map[string][]string)
	for key, value := range data {
		switch v := value.(type) {
		case string:
			if firstElement {
				strToHash += value.(string)
				firstElement = false
			} else {
				strToHash += "." + value.(string)
			}
			queries[key] = []string{v}
		case map[string]interface{}:
			for k, v := range v {
				// convert to string
				var str string
				switch v := v.(type) {
				case float64:
					str = fmt.Sprintf("%d", int(v))
				case string:
					str = v
				}
				queries[fmt.Sprintf("%s[%s]", key, k)] = []string{str}
			}
		}
	}

	if strToHash != "" {
		// Generate the hash
		fmt.Println("strToHash", strToHash)
		hash := pkg.GenerateHash(secret, strToHash)
		queries["hash"] = []string{hash}
	}

	return queries, nil
}
