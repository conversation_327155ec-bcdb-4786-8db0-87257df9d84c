package tasks

import (
	"context"

	"github.com/continue-team/hecarim/internal"
	"github.com/continue-team/hecarim/internal/repositories"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/pkg/util"
	r "github.com/continue-team/riot/rabbitmq"
	"go.uber.org/zap"
)

type Task struct {
	util     riot.Util
	userRepo *repositories.UserRepository
}

func NewTask(
	util riot.Util,
	userRepo *repositories.UserRepository,
) *Task {
	return &Task{
		util:     util,
		userRepo: userRepo,
	}
}

func (c *Task) TaskEvtUpdateMerchant(route string, evt r.PayloadEvent) error {

	var updateMerchant internal.PayloadUpdateMerchant

	err := util.ConvertToStruct(evt.Data, &updateMerchant)

	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.ConvertToStruct", zap.Error(err))
	}

	filterUser := map[string]interface{}{
		"parent_id": updateMerchant.CompanyID,
		"id":        updateMerchant.ProfileID,
	}

	updateUser := internal.CreateUser{
		MerchantID: &updateMerchant.MerchantID,
	}

	_, err = c.userRepo.UpdateByFilter(context.Background(), filterUser, updateUser)

	if err != nil {
		return riot.WrapErrorfLog(c.util.Logger, err, riot.ErrorCodeUnknown, "Task.TaskEvtUpdateMerchant")
	}

	return nil
}
