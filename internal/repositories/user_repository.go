package repositories

import (
	"context"
	"fmt"

	"github.com/continue-team/hecarim/internal"
	"github.com/continue-team/hecarim/models"
	"github.com/continue-team/hecarim/pkg"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"
	"github.com/continue-team/riot/pkg/logger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type UserRepository struct {
	util riot.Util
}

func NewUserRepository(util riot.Util) *UserRepository {
	return &UserRepository{
		util: util,
	}
}

func (r *UserRepository) Create(ctx context.Context, params internal.CreateUser) (internal.User, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("UserRepository.Repo", "Create"), zap.Any("params", params))

	model, err := mapUserFromParams(params, "create")

	if err != nil {
		return internal.User{}, err
	}

	// Using WithContext to attach the provided context to the database operation
	err = model.Create(ctx, r.util.DB, models.UserCollection, &model)
	if err != nil {
		return internal.User{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "UserRepository.Create")
	}
	return mapUserToInternalData(model), nil
}

func (r *UserRepository) CreateBulk(ctx context.Context, params []internal.CreateUser) ([]internal.User, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("UserRepository.Repo", "CreateBulk"), zap.Any("params", params))

	var listModels []interface{}

	for _, param := range params {

		model, err := mapUserFromParams(param, "create")

		if err != nil {
			return []internal.User{}, err
		}

		listModels = append(listModels, &model)
	}

	// Using WithContext to attach the provided context to the database operation
	_, err := (&models.User{}).CreateBulk(ctx, r.util.DB, models.UserCollection, listModels)

	if err != nil {
		return []internal.User{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "UserRepository.CreateBulk")
	}

	return []internal.User{}, nil
}

func (r *UserRepository) Update(ctx context.Context, id string, params internal.CreateUser) (internal.User, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("UserRepository.Repo", "Update"))

	model, err := mapUserFromParams(params, "update")

	if err != nil {
		return internal.User{}, err
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return internal.User{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "Update.ObjectIDFromHex.idHex")
	}

	err = model.Update(ctx, r.util.DB, models.UserCollection, bson.M{"_id": objectID}, &model)

	if err != nil {
		return internal.User{}, err
	}

	return r.GetByID(ctx, id)
}

func (r *UserRepository) UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateUser) (internal.User, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("UserRepository", "UpdateByFilter"))

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.User{}, err
			}
			bsonM["_id"] = objectID
		case "parent_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.User{}, err
			}
			bsonM["parent_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	model, err := mapUserFromParams(params, "update")

	if err != nil {
		return internal.User{}, err
	}

	err = model.Update(ctx, r.util.DB, models.UserCollection, bsonM, &model)

	if err != nil {
		return internal.User{}, err
	}

	return r.GetByFilter(ctx, filter)
}

func (r *UserRepository) SoftDelete(ctx context.Context, id string) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("UserRepository", "SoftDelete"))
	var model models.User
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserRepository.SoftDelete.idHex")
	}

	return model.SoftDelete(ctx, r.util.DB, models.UserCollection, bson.M{"_id": idHex})
}

func (r *UserRepository) SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("UserRepository", "SoftDeleteByFilter"))

	var model models.User

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["_id"] = objectID
		case "parent_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["parent_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.SoftBulkDelete(ctx, r.util.DB, models.UserCollection, bsonM)
}

func (r *UserRepository) GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.User, error) {
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("UserRepository", "GetByFilter"))

	var model models.User

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.User{}, err
			}
			bsonM["_id"] = objectID
		case "parent_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.User{}, err
			}
			bsonM["parent_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.First(ctx, r.util.DB, models.UserCollection, bsonM, &model)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return internal.User{}, nil
		}

		return internal.User{}, err
	}

	return mapUserToInternalData(model), nil
}

func (r *UserRepository) SumByFilter(ctx context.Context, filter map[string]interface{}) (float64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("UserRepository", "SumByFilter"))

	var model models.User

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "parent_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["parent_id"] = objectID
		default:
			bsonM[key] = value
		}
	}
	total, err := model.SumByFilter(ctx, r.util.DB, models.UserCollection, "size", bsonM)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return 0, nil
		}

		return 0, err
	}

	return total, nil
}

func (r *UserRepository) CountByFilter(ctx context.Context, filter map[string]interface{}) (int64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("UserRepository", "CountByFilter"))

	var model models.User

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "parent_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["parent_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.Count(ctx, r.util.DB, models.UserCollection, bsonM)
}

func (r *UserRepository) GetByID(ctx context.Context, id string) (internal.User, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("UserRepository", "GetByID"))

	var model models.User
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserRepository.GetByID.idHex")
	}

	err = model.First(ctx, r.util.DB, models.UserCollection, bson.M{"_id": idHex}, &model)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.User{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.User{}, err
	}
	return mapUserToInternalData(model), nil
}

func (r *UserRepository) GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.User, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("UserRepository", "GetAll"))
	var model models.User
	var listModels []models.User

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.User{}, err
			}
			bsonM["_id"] = objectID
		case "parent_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.User{}, err
			}
			bsonM["parent_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAll(ctx, r.util.DB, models.UserCollection, bsonM, &listModels)
	if err != nil {
		return []internal.User{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "UserRepository.FindAll")
	}

	var internalModels []internal.User

	for _, externalModel := range listModels {
		internalModel := mapUserToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *UserRepository) GetAllWithSoftDelete(ctx context.Context, filter map[string]interface{}) ([]internal.User, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("UserRepository", "GetAll"))
	var model models.User
	var listModels []models.User

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.User{}, err
			}
			bsonM["_id"] = objectID
		case "parent_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.User{}, err
			}
			bsonM["parent_id"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAllWithSoftDeleteOption(ctx, r.util.DB, models.UserCollection, bsonM, &listModels, false)
	if err != nil {
		return []internal.User{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "UserRepository.FindAllWithSoftDeleteOption")
	}
	var internalModels []internal.User

	for _, externalModel := range listModels {
		internalModel := mapUserToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *UserRepository) GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.UserPagin, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("UserRepository", "GetAllWithPagination"))
	var listModels []models.User
	var model models.User

	resp, err := model.FindAllWithPagination(ctx, r.util.DB, models.UserCollection, paginOpt, &listModels)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.UserPagin{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.UserPagin{}, err
	}

	var internalModels []internal.User

	for _, externalModel := range listModels {
		internalModel := mapUserToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internal.UserPagin{
		Limit:        resp.Limit,
		Page:         resp.Page,
		Sort:         resp.Sort,
		TotalRecords: int(resp.TotalRecords),
		TotalPages:   resp.TotalPages,
		Records:      internalModels,
	}, nil
}

func mapUserToInternalData(data models.User) internal.User {
	return internal.User{
		ID:                  data.ID.Hex(),
		ParentID:            data.ParentID.Hex(),
		ParentRole:          pkg.GetIntValue(data.ParentRole, 0),
		ParentUsername:      pkg.GetStringValue(data.ParentUsername, ""),
		Role:                pkg.GetIntValue(data.Role, 0),
		Username:            pkg.GetStringValue(data.Username, ""),
		LoginID:             pkg.GetStringValue(data.LoginID, ""),
		Alias:               pkg.GetStringValue(data.Alias, ""),
		FirstName:           pkg.GetStringValue(data.FirstName, ""),
		LastName:            pkg.GetStringValue(data.LastName, ""),
		NickName:            pkg.GetStringValue(data.NickName, ""),
		Email:               pkg.GetStringValue(data.Email, ""),
		MobilePhone:         pkg.GetStringValue(data.MobilePhone, ""),
		MobilePhoneCountry:  pkg.GetStringValue(data.MobilePhoneCountry, ""),
		MobilePhoneCode:     pkg.GetStringValue(data.MobilePhoneCode, ""),
		MobilePhoneNumber:   pkg.GetStringValue(data.MobilePhoneNumber, ""),
		MobilePhoneVerified: pkg.GetBoolValue(data.MobilePhoneVerified, false),
		Gender:              pkg.GetIntValue(data.Gender, 0),
		Type:                pkg.GetIntValue(data.Type, 0),
		Status:              pkg.GetBoolValue(data.Status, false),
		ParentBlocked:       pkg.GetBoolValue(data.Status, false),
		CreatedIP:           pkg.GetStringValue(data.CreatedIP, ""),
		LastLoggedIn:        data.LastLoggedIn,
		FirstTransaction:    pkg.GetBoolValue(data.FirstTransaction, false),
		Group:               pkg.GetStringValue(data.Group, ""),
		Referrer:            pkg.GetStringValue(data.Referrer, ""),
		Avatar:              pkg.GetStringValue(data.Avatar, ""),
		Bookmark:            pkg.GetStringValue(data.Bookmark, ""),
		Bank:                pkg.GetStringValue(data.Bank, ""),
		AccountNumber:       pkg.GetStringValue(data.AccountNumber, ""),
		AccountName:         pkg.GetStringValue(data.AccountName, ""),
		Mtc:                 pkg.GetStringValue(data.Mtc, ""),
		LastLoggedInIP:      pkg.GetStringValue(data.LastLoggedInIP, ""),
		TermsAccepted:       pkg.GetBoolValue(data.TermsAccepted, false),
		IsSocialMedia:       pkg.GetBoolValue(data.IsSocialMedia, false),
		ReferrerCode:        pkg.GetStringValue(data.ReferrerCode, ""),
		ReferrerID: func() string {

			if data.ReferrerID != nil {
				return data.ReferrerID.Hex()
			}
			return ""
		}(),
		MarketingCode: pkg.GetStringValue(data.MarketingCode, ""),
		MarketingID: func() string {

			if data.MarketingID != nil {
				return data.MarketingID.Hex()
			}
			return ""
		}(),
		VoucherCode: pkg.GetStringValue(data.VoucherCode, ""),
		VoucherID: func() string {
			if data.VoucherID != nil {
				return data.VoucherID.Hex()
			}
			return ""
		}(),
		MerchantID: func() string {
			if data.MerchantID != nil {
				return data.MerchantID.Hex()
			}
			return ""
		}(),
		CreatedAt: data.CreatedAt,
		UpdatedAt: data.UpdatedAt,
	}
}

func mapUserFromParams(params internal.CreateUser, action string) (models.User, error) {
	fmt.Println(action)

	mobileCode := pkg.SetStringPointer(params.MobilePhoneCode, action)
	mobilePhone := pkg.FilterMobilePhone(params.MobilePhone, mobileCode)

	model := models.User{
		ParentRole:          pkg.SetIntPointer(params.ParentRole, action),
		ParentUsername:      pkg.SetStringPointer(params.ParentUsername, ""),
		Role:                pkg.SetIntPointer(params.Role, action),
		Username:            pkg.FilterUsername(pkg.SetStringPointer(params.Username, action)),
		LoginID:             pkg.FilterUsername(pkg.SetStringPointer(params.LoginID, action)),
		Alias:               pkg.FilterAndUppercaseAlias(pkg.SetStringUpperPointer(params.Alias, action)),
		FirstName:           pkg.FilterAndUppercase(pkg.SetStringPointer(params.FirstName, action)),
		LastName:            pkg.FilterAndUppercase(pkg.SetStringPointer(params.LastName, action)),
		NickName:            pkg.FilterAndUppercase(pkg.SetStringPointer(params.NickName, action)),
		Email:               pkg.SetStringUpperPointer(params.Email, action),
		MobilePhone:         mobilePhone,
		MobilePhoneCountry:  pkg.FilterMobilePhoneCountry(pkg.SetStringPointer(params.MobilePhoneCountry, action)),
		MobilePhoneCode:     mobileCode,
		MobilePhoneNumber:   pkg.FilterMobilePhoneNumber(mobileCode, mobilePhone),
		MobilePhoneVerified: pkg.SetBoolPointer(params.MobilePhoneVerified, action),
		Gender:              pkg.SetIntPointer(params.Gender, action),
		Type:                pkg.SetIntPointer(params.Type, action),
		ParentBlocked:       pkg.SetBoolPointer(params.ParentBlocked, action),
		CreatedIP:           pkg.FilterIP(pkg.SetStringPointer(params.CreatedIP, action)),
		FirstTransaction:    pkg.SetBoolPointer(params.FirstTransaction, action),
		Group:               pkg.SetStringPointer(params.Group, action),
		Avatar:              pkg.SetStringPointer(params.Avatar, action),
		Bookmark:            pkg.SetStringPointer(params.Bookmark, action),
		Bank:                pkg.SetStringPointer(params.Bank, action),
		AccountNumber:       pkg.SetStringPointer(params.AccountNumber, action),
		AccountName:         pkg.SetStringPointer(params.AccountName, action),
		Mtc:                 pkg.SetStringPointer(params.Mtc, action),
		ReferrerCode:        pkg.SetStringUpperPointer(params.ReferrerCode, action),
		MarketingCode:       pkg.SetStringUpperPointer(params.MarketingCode, action),
		VoucherCode:         pkg.SetStringUpperPointer(params.VoucherCode, action),
		LastLoggedInIP:      pkg.SetStringPointer(params.LastLoggedInIP, action),
		LastLoggedIn:        params.LastLoggedIn,
		IsSocialMedia:       pkg.SetBoolPointer(params.IsSocialMedia, action),
		TermsAccepted:       pkg.SetBoolPointer(params.TermsAccepted, action),
		Status:              pkg.SetBoolPointer(params.Status, action),
	}

	if params.Company != "" {
		objectID, err := primitive.ObjectIDFromHex(params.Company)
		if err != nil {
			return models.User{}, err
		}
		model.Company = objectID
	}

	if params.ParentID != "" {
		objectID, err := primitive.ObjectIDFromHex(params.ParentID)
		if err != nil {
			return models.User{}, err
		}
		model.ParentID = objectID
	}

	if params.ReferrerID != nil {
		objectID, err := primitive.ObjectIDFromHex(*params.ReferrerID)
		if err != nil {
			return models.User{}, err
		}
		model.ReferrerID = &objectID
	}

	if params.MarketingID != nil {
		objectID, err := primitive.ObjectIDFromHex(*params.MarketingID)
		if err != nil {
			return models.User{}, err
		}
		model.MarketingID = &objectID
	}

	if params.VoucherID != nil {
		objectID, err := primitive.ObjectIDFromHex(*params.VoucherID)
		if err != nil {
			return models.User{}, err
		}
		model.VoucherID = &objectID
	}

	if params.MerchantID != nil {
		objectID, err := primitive.ObjectIDFromHex(*params.MerchantID)
		if err != nil {
			return models.User{}, err
		}
		model.MerchantID = &objectID
	}

	return model, nil
}
