package repositories

import (
	"context"
	"fmt"

	"github.com/continue-team/hecarim/internal"
	"github.com/continue-team/hecarim/models"
	"github.com/continue-team/hecarim/pkg"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"
	"github.com/continue-team/riot/pkg/logger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type UserSecurityRepository struct {
	util riot.Util
}

func NewUserSecurityRepository(util riot.Util) *UserSecurityRepository {
	return &UserSecurityRepository{
		util: util,
	}
}

func (r *UserSecurityRepository) Create(ctx context.Context, params internal.CreateUserSecurity) (internal.UserSecurity, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("UserSecurityRepository.Repo", "Create"), zap.Any("params", params))

	model, err := mapUserSecurityFromParams(params, "create")

	if err != nil {
		return internal.UserSecurity{}, err
	}

	// Using WithContext to attach the provided context to the database operation
	err = model.Create(ctx, r.util.DB, models.UserSecurityCollection, &model)
	if err != nil {
		return internal.UserSecurity{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "UserSecurityRepository.Create")
	}
	return mapUserSecurityToInternalData(model), nil
}

func (r *UserSecurityRepository) CreateBulk(ctx context.Context, params []internal.CreateUserSecurity) ([]internal.UserSecurity, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("UserSecurityRepository.Repo", "CreateBulk"), zap.Any("params", params))

	var listModels []interface{}

	for _, param := range params {

		model, err := mapUserSecurityFromParams(param, "create")

		if err != nil {
			return []internal.UserSecurity{}, err
		}

		listModels = append(listModels, &model)
	}

	// Using WithContext to attach the provided context to the database operation
	_, err := (&models.UserSecurity{}).CreateBulk(ctx, r.util.DB, models.UserSecurityCollection, listModels)

	if err != nil {
		return []internal.UserSecurity{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "UserSecurityRepository.CreateBulk")
	}

	return []internal.UserSecurity{}, nil
}

func (r *UserSecurityRepository) Update(ctx context.Context, id string, params internal.CreateUserSecurity) (internal.UserSecurity, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("UserSecurityRepository.Repo", "Update"))

	model, err := mapUserSecurityFromParams(params, "update")

	if err != nil {
		return internal.UserSecurity{}, err
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return internal.UserSecurity{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "Update.ObjectIDFromHex.idHex")
	}

	model.Update(ctx, r.util.DB, models.UserSecurityCollection, bson.M{"_id": objectID}, &model)

	return r.GetByID(ctx, id)
}

func (r *UserSecurityRepository) UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateUserSecurity) (internal.UserSecurity, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("UserSecurityRepository", "UpdateByFilter"))

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.UserSecurity{}, err
			}
			bsonM["_id"] = objectID
		case "parent":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.UserSecurity{}, err
			}
			bsonM["parent"] = objectID
		case "user":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.UserSecurity{}, err
			}
			bsonM["user"] = objectID
		default:
			bsonM[key] = value
		}
	}

	model, err := mapUserSecurityFromParams(params, "update")

	if err != nil {
		return internal.UserSecurity{}, err
	}

	model.Update(ctx, r.util.DB, models.UserSecurityCollection, bsonM, &model)

	return r.GetByFilter(ctx, filter)
}

func (r *UserSecurityRepository) SoftDelete(ctx context.Context, id string) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("UserSecurityRepository", "SoftDelete"))
	var model models.UserSecurity
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserSecurityRepository.SoftDelete.idHex")
	}

	return model.SoftDelete(ctx, r.util.DB, models.UserSecurityCollection, bson.M{"_id": idHex})
}

func (r *UserSecurityRepository) SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("UserSecurityRepository", "SoftDeleteByFilter"))

	var model models.UserSecurity

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["_id"] = objectID
		case "parent_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["parent_id"] = objectID
		case "user":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["user"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.SoftBulkDelete(ctx, r.util.DB, models.UserSecurityCollection, bsonM)
}

func (r *UserSecurityRepository) GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.UserSecurity, error) {
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("UserSecurityRepository", "GetByFilter"))

	var model models.UserSecurity

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.UserSecurity{}, err
			}
			bsonM["_id"] = objectID
		case "parent_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.UserSecurity{}, err
			}
			bsonM["parent_id"] = objectID
		case "user":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.UserSecurity{}, err
			}
			bsonM["user"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.First(ctx, r.util.DB, models.UserSecurityCollection, bsonM, &model)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return internal.UserSecurity{}, nil
		}

		return internal.UserSecurity{}, err
	}

	return mapUserSecurityToInternalData(model), nil
}

func (r *UserSecurityRepository) SumByFilter(ctx context.Context, filter map[string]interface{}) (float64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("UserSecurityRepository", "SumByFilter"))

	var model models.UserSecurity

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "parent_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["parent_id"] = objectID
		case "user":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["user"] = objectID
		default:
			bsonM[key] = value
		}
	}
	total, err := model.SumByFilter(ctx, r.util.DB, models.UserSecurityCollection, "size", bsonM)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return 0, nil
		}

		return 0, err
	}

	return total, nil
}

func (r *UserSecurityRepository) CountByFilter(ctx context.Context, filter map[string]interface{}) (int64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("UserSecurityRepository", "CountByFilter"))

	var model models.UserSecurity

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "parent_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["parent_id"] = objectID
		case "user":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["user"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.Count(ctx, r.util.DB, models.UserSecurityCollection, bsonM)
}

func (r *UserSecurityRepository) GetByID(ctx context.Context, id string) (internal.UserSecurity, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("UserSecurityRepository", "GetByID"))

	var model models.UserSecurity
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserSecurityRepository.GetByID.idHex")
	}

	err = model.First(ctx, r.util.DB, models.UserSecurityCollection, bson.M{"_id": idHex}, &model)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.UserSecurity{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.UserSecurity{}, err
	}
	return mapUserSecurityToInternalData(model), nil
}

func (r *UserSecurityRepository) GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.UserSecurity, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("UserSecurityRepository", "GetAll"))
	var model models.UserSecurity
	var listModels []models.UserSecurity

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.UserSecurity{}, err
			}
			bsonM["_id"] = objectID
		case "parent_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.UserSecurity{}, err
			}
			bsonM["parent_id"] = objectID
		case "user":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.UserSecurity{}, err
			}
			bsonM["user"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAll(ctx, r.util.DB, models.UserSecurityCollection, bsonM, &listModels)
	if err != nil {
		return []internal.UserSecurity{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "UserSecurityRepository.FindAll")
	}

	var internalModels []internal.UserSecurity

	for _, externalModel := range listModels {
		internalModel := mapUserSecurityToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *UserSecurityRepository) GetAllWithSoftDelete(ctx context.Context, filter map[string]interface{}) ([]internal.UserSecurity, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("UserSecurityRepository", "GetAll"))
	var model models.UserSecurity
	var listModels []models.UserSecurity

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.UserSecurity{}, err
			}
			bsonM["_id"] = objectID
		case "parent_id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.UserSecurity{}, err
			}
			bsonM["parent_id"] = objectID
		case "user":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.UserSecurity{}, err
			}
			bsonM["user"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAllWithSoftDeleteOption(ctx, r.util.DB, models.UserSecurityCollection, bsonM, &listModels, false)
	if err != nil {
		return []internal.UserSecurity{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "UserSecurityRepository.FindAllWithSoftDeleteOption")
	}
	var internalModels []internal.UserSecurity

	for _, externalModel := range listModels {
		internalModel := mapUserSecurityToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *UserSecurityRepository) GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.UserSecurityPagin, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("UserSecurityRepository", "GetAllWithPagination"))
	var listModels []models.UserSecurity
	var model models.UserSecurity

	resp, err := model.FindAllWithPagination(ctx, r.util.DB, models.UserSecurityCollection, paginOpt, &listModels)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.UserSecurityPagin{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.UserSecurityPagin{}, err
	}

	var internalModels []internal.UserSecurity

	for _, externalModel := range listModels {
		internalModel := mapUserSecurityToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internal.UserSecurityPagin{
		Limit:        resp.Limit,
		Page:         resp.Page,
		Sort:         resp.Sort,
		TotalRecords: int(resp.TotalRecords),
		TotalPages:   resp.TotalPages,
		Records:      internalModels,
	}, nil
}

func mapUserSecurityToInternalData(data models.UserSecurity) internal.UserSecurity {
	return internal.UserSecurity{
		ID:            data.ID.Hex(),
		User:          data.User.Hex(),
		LoginID:       pkg.GetStringValue(data.LoginID, ""),
		Alias:         pkg.GetStringValue(data.Alias, ""),
		ResetLoginID:  pkg.GetBoolValue(data.ResetLoginID, false),
		Password:      pkg.GetStringValue(data.Password, ""),
		ResetPassword: pkg.GetBoolValue(data.ResetPassword, false),
		Pin:           pkg.GetStringValue(data.Pin, ""),
		ResetPin:      pkg.GetBoolValue(data.ResetPin, false),
		WhitelistIP:   pkg.GetStringValue(data.WhitelistIP, ""),
		CreatedIP:     pkg.GetStringValue(data.CreatedIP, ""),
		CreatedAt:     data.CreatedAt,
		UpdatedAt:     data.UpdatedAt,
	}
}

func mapUserSecurityFromParams(params internal.CreateUserSecurity, action string) (models.UserSecurity, error) {
	fmt.Println(action)
	model := models.UserSecurity{
		LoginID:       pkg.FilterUsername(pkg.SetStringPointer(params.LoginID, action)),
		Alias:         pkg.FilterAndUppercaseAlias(pkg.SetStringPointer(params.Alias, action)),
		ResetLoginID:  pkg.SetBoolPointer(params.ResetLoginID, action),
		Password:      pkg.SetStringPointer(params.Password, action),
		ResetPassword: pkg.SetBoolPointer(params.ResetPassword, action),
		Pin:           pkg.FilterPin(pkg.SetStringPointer(params.Pin, action)),
		ResetPin:      pkg.SetBoolPointer(params.ResetPin, action),
		WhitelistIP:   pkg.FilterWhitelistIP(pkg.SetStringPointer(params.WhitelistIP, action)),
		CreatedIP:     pkg.FilterIP(pkg.SetStringPointer(params.CreatedIP, action)),
	}

	if params.User != "" {
		objectID, err := primitive.ObjectIDFromHex(params.User)
		if err != nil {
			return models.UserSecurity{}, err
		}
		model.User = objectID
	}

	return model, nil
}
