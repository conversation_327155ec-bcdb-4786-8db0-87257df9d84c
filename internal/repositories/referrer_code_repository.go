package repositories

import (
	"context"

	"github.com/continue-team/hecarim/internal"
	"github.com/continue-team/hecarim/models"
	"github.com/continue-team/hecarim/pkg"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"
	"github.com/continue-team/riot/pkg/logger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type ReferrerCodeRepository struct {
	util riot.Util
}

func NewReferrerCodeRepository(util riot.Util) *ReferrerCodeRepository {
	return &ReferrerCodeRepository{
		util: util,
	}
}

func (r *ReferrerCodeRepository) Create(ctx context.Context, params internal.CreateReferrerCode) (internal.ReferrerCode, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ReferrerCodeRepository.Repo", "Create"), zap.Any("params", params))

	model, err := mapReferralCodeFromParams(params, "create")

	if err != nil {
		return internal.ReferrerCode{}, err
	}

	// Using WithContext to attach the provided context to the database operation
	err = model.Create(ctx, r.util.DB, models.ReferrerCodeCollection, &model)
	if err != nil {
		return internal.ReferrerCode{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "ReferrerCodeRepository.Create")
	}
	return mapReferralCodeToInternalData(model), nil
}

func (r *ReferrerCodeRepository) CreateBulk(ctx context.Context, params []internal.CreateReferrerCode) ([]internal.ReferrerCode, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ReferrerCodeRepository.Repo", "CreateBulk"), zap.Any("params", params))

	var listModels []interface{}

	for _, param := range params {

		model, err := mapReferralCodeFromParams(param, "create")

		if err != nil {
			return []internal.ReferrerCode{}, err
		}

		listModels = append(listModels, &model)
	}

	// Using WithContext to attach the provided context to the database operation
	_, err := (&models.ReferrerCode{}).CreateBulk(ctx, r.util.DB, models.ReferrerCodeCollection, listModels)

	if err != nil {
		return []internal.ReferrerCode{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "ReferrerCodeRepository.CreateBulk")
	}

	return []internal.ReferrerCode{}, nil
}

func (r *ReferrerCodeRepository) Update(ctx context.Context, id string, params internal.CreateReferrerCode) (internal.ReferrerCode, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ReferrerCodeRepository.Repo", "Update"))

	model, err := mapReferralCodeFromParams(params, "update")

	if err != nil {
		return internal.ReferrerCode{}, err
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return internal.ReferrerCode{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "Update.ObjectIDFromHex.idHex")
	}

	model.Update(ctx, r.util.DB, models.ReferrerCodeCollection, bson.M{"_id": objectID}, &model)

	return r.GetByID(ctx, id)
}

func (r *ReferrerCodeRepository) UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateReferrerCode) (internal.ReferrerCode, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ReferrerCodeRepository", "UpdateByFilter"))

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.ReferrerCode{}, err
			}
			bsonM["_id"] = objectID
		case "profile":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.ReferrerCode{}, err
			}
			bsonM["profile"] = objectID
		default:
			bsonM[key] = value
		}
	}

	model, err := mapReferralCodeFromParams(params, "update")

	if err != nil {
		return internal.ReferrerCode{}, err
	}

	model.Update(ctx, r.util.DB, models.ReferrerCodeCollection, bsonM, &model)

	return mapReferralCodeToInternalData(model), nil
}

func (r *ReferrerCodeRepository) SoftDelete(ctx context.Context, id string) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ReferrerCodeRepository", "SoftDelete"))
	var model models.ReferrerCode
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "ReferrerCodeRepository.SoftDelete.idHex")
	}

	return model.SoftDeleteDefault(ctx, r.util.DB, models.ReferrerCodeCollection, bson.M{"_id": idHex})
}

func (r *ReferrerCodeRepository) SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ReferrerCodeRepository", "SoftDeleteByFilter"))

	var model models.ReferrerCode

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["_id"] = objectID
		case "profile":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["profile"] = objectID

		default:
			bsonM[key] = value
		}
	}

	return model.SoftBulkDelete(ctx, r.util.DB, models.ReferrerCodeCollection, bsonM)
}

func (r *ReferrerCodeRepository) GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.ReferrerCode, error) {
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ReferrerCodeRepository", "GetByFilter"))

	var model models.ReferrerCode

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.ReferrerCode{}, err
			}
			bsonM["_id"] = objectID
		case "profile":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.ReferrerCode{}, err
			}
			bsonM["profile"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.First(ctx, r.util.DB, models.ReferrerCodeCollection, bsonM, &model)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return internal.ReferrerCode{}, nil
		}

		return internal.ReferrerCode{}, err
	}

	return mapReferralCodeToInternalData(model), nil
}

func (r *ReferrerCodeRepository) SumByFilter(ctx context.Context, filter map[string]interface{}) (float64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ReferrerCodeRepository", "SumByFilter"))

	var model models.ReferrerCode

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile"] = objectID
		default:
			bsonM[key] = value
		}
	}
	total, err := model.SumByFilter(ctx, r.util.DB, models.ReferrerCodeCollection, "size", bsonM)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return 0, nil
		}

		return 0, err
	}

	return total, nil
}

func (r *ReferrerCodeRepository) CountByFilter(ctx context.Context, filter map[string]interface{}) (int64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ReferrerCodeRepository", "CountByFilter"))

	var model models.ReferrerCode

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.Count(ctx, r.util.DB, models.ReferrerCodeCollection, bsonM)
}

func (r *ReferrerCodeRepository) GetByID(ctx context.Context, id string) (internal.ReferrerCode, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ReferrerCodeRepository", "GetByID"))

	var model models.ReferrerCode
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "ReferrerCodeRepository.GetByID.idHex")
	}

	err = model.First(ctx, r.util.DB, models.ReferrerCodeCollection, bson.M{"_id": idHex}, &model)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.ReferrerCode{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.ReferrerCode{}, err
	}
	return mapReferralCodeToInternalData(model), nil
}

func (r *ReferrerCodeRepository) GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.ReferrerCode, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ReferrerCodeRepository", "GetAll"))
	var model models.ReferrerCode
	var listModels []models.ReferrerCode

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.ReferrerCode{}, err
			}
			bsonM["_id"] = objectID
		case "profile":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.ReferrerCode{}, err
			}
			bsonM["profile"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAll(ctx, r.util.DB, models.ReferrerCodeCollection, bsonM, &listModels)
	if err != nil {
		return []internal.ReferrerCode{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "ReferrerCodeRepository.FindAll")
	}

	var internalModels []internal.ReferrerCode

	for _, externalModel := range listModels {
		internalModel := mapReferralCodeToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *ReferrerCodeRepository) GetAllWithSoftDelete(ctx context.Context, filter map[string]interface{}) ([]internal.ReferrerCode, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ReferrerCodeRepository", "GetAll"))
	var model models.ReferrerCode
	var listModels []models.ReferrerCode

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.ReferrerCode{}, err
			}
			bsonM["_id"] = objectID
		case "profile":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.ReferrerCode{}, err
			}
			bsonM["profile"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAllWithSoftDeleteOption(ctx, r.util.DB, models.ReferrerCodeCollection, bsonM, &listModels, false)
	if err != nil {
		return []internal.ReferrerCode{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "ReferrerCodeRepository.FindAllWithSoftDeleteOption")
	}
	var internalModels []internal.ReferrerCode

	for _, externalModel := range listModels {
		internalModel := mapReferralCodeToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *ReferrerCodeRepository) GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.ReferrerCodePagin, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("ReferrerCodeRepository", "GetAllWithPagination"))
	var listModels []models.ReferrerCode
	var model models.ReferrerCode

	resp, err := model.FindAllWithPagination(ctx, r.util.DB, models.ReferrerCodeCollection, paginOpt, &listModels)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.ReferrerCodePagin{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.ReferrerCodePagin{}, err
	}

	var internalModels []internal.ReferrerCode

	for _, externalModel := range listModels {
		internalModel := mapReferralCodeToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internal.ReferrerCodePagin{
		Limit:        resp.Limit,
		Page:         resp.Page,
		Sort:         resp.Sort,
		TotalRecords: int(resp.TotalRecords),
		TotalPages:   resp.TotalPages,
		Records:      internalModels,
	}, nil
}

func mapReferralCodeToInternalData(data models.ReferrerCode) internal.ReferrerCode {
	return internal.ReferrerCode{
		ID:          data.ID.Hex(),
		Profile:     data.Profile.Hex(),
		Code:        pkg.GetStringValue(data.Code, ""),
		Description: pkg.GetStringValue(data.Description, ""),
		Status:      pkg.GetBoolValue(data.Status, false),
		ValidUntil:  data.ValidUntil,
		CreatedAt:   data.CreatedAt,
		UpdatedAt:   data.UpdatedAt,
	}
}

func mapReferralCodeFromParams(params internal.CreateReferrerCode, action string) (models.ReferrerCode, error) {

	model := models.ReferrerCode{
		Code:        pkg.SetStringUpperPointer(params.Code, action),
		Description: pkg.SetStringPointer(params.Description, action),
		Status:      pkg.SetBoolPointer(params.Status, action),
	}

	if params.Profile != "" {
		objectID, err := primitive.ObjectIDFromHex(params.Profile)
		if err != nil {
			return models.ReferrerCode{}, err
		}
		model.Profile = objectID
	}

	if params.ValidUntil != nil {
		validUtil := params.ValidUntil
		parsedTime, err := pkg.ParseDateString(*validUtil, pkg.DateLayoutDefault)
		if err != nil {
			return models.ReferrerCode{}, err
		}

		model.ValidUntil = &parsedTime
	}

	return model, nil
}
