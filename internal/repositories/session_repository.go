package repositories

import (
	"context"
	"fmt"

	"github.com/continue-team/hecarim/internal"
	"github.com/continue-team/hecarim/models"
	"github.com/continue-team/hecarim/pkg"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"
	"github.com/continue-team/riot/pkg/logger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
)

type SessionRepository struct {
	util riot.Util
}

func NewSessionRepository(util riot.Util) *SessionRepository {
	return &SessionRepository{
		util: util,
	}
}

func (r *SessionRepository) Create(ctx context.Context, params internal.CreateSession) (internal.Session, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("SessionRepository.Repo", "Create"), zap.Any("params", params))

	model, err := mapSessionFromParams(params, "create")

	if err != nil {
		return internal.Session{}, err
	}

	// Using WithContext to attach the provided context to the database operation
	err = model.Create(ctx, r.util.DB, models.SessionCollection, &model)
	if err != nil {
		return internal.Session{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "SessionRepository.Create")
	}
	return mapSessionToInternalData(model), nil
}

func (r *SessionRepository) CreateBulk(ctx context.Context, params []internal.CreateSession) ([]internal.Session, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("SessionRepository.Repo", "CreateBulk"), zap.Any("params", params))

	var listModels []interface{}

	for _, param := range params {

		model, err := mapSessionFromParams(param, "create")

		if err != nil {
			return []internal.Session{}, err
		}

		listModels = append(listModels, &model)
	}

	// Using WithContext to attach the provided context to the database operation
	_, err := (&models.Session{}).CreateBulk(ctx, r.util.DB, models.SessionCollection, listModels)

	if err != nil {
		return []internal.Session{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "SessionRepository.CreateBulk")
	}

	return []internal.Session{}, nil
}

func (r *SessionRepository) Update(ctx context.Context, id string, params internal.CreateSession) (internal.Session, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("SessionRepository.Repo", "Update"))

	model, err := mapSessionFromParams(params, "update")

	if err != nil {
		return internal.Session{}, err
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return internal.Session{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "Update.ObjectIDFromHex.idHex")
	}

	model.Update(ctx, r.util.DB, models.SessionCollection, bson.M{"_id": objectID}, &model)

	return r.GetByID(ctx, id)
}

func (r *SessionRepository) UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateSession) (internal.Session, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("SessionRepository", "UpdateByFilter"))

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Session{}, err
			}
			bsonM["_id"] = objectID
		case "parent":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Session{}, err
			}
			bsonM["parent"] = objectID
		case "user":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Session{}, err
			}
			bsonM["user"] = objectID
		default:
			bsonM[key] = value
		}
	}

	model, err := mapSessionFromParams(params, "update")

	if err != nil {
		return internal.Session{}, err
	}

	if err := model.Update(ctx, r.util.DB, models.SessionCollection, bsonM, &model); err != nil {
		return internal.Session{}, err
	}

	return r.GetByFilter(ctx, filter)
}

func (r *SessionRepository) UpdateManyByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateSession) (internal.Session, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("SessionRepository", "UpdateByFilter"))

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Session{}, err
			}
			bsonM["_id"] = objectID
		case "parent":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Session{}, err
			}
			bsonM["parent"] = objectID
		case "user":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Session{}, err
			}
			bsonM["user"] = objectID
		default:
			bsonM[key] = value
		}
	}

	model, err := mapSessionFromParams(params, "update")

	if err != nil {
		return internal.Session{}, err
	}

	if err := model.UpdateMany(ctx, r.util.DB, models.SessionCollection, bsonM, &model); err != nil {
		return internal.Session{}, err
	}

	return r.GetByFilter(ctx, filter)
}

func (r *SessionRepository) SoftDelete(ctx context.Context, id string) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("SessionRepository", "SoftDelete"))
	var model models.Session
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "SessionRepository.SoftDelete.idHex")
	}

	return model.SoftDelete(ctx, r.util.DB, models.SessionCollection, bson.M{"_id": idHex})
}

func (r *SessionRepository) SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("SessionRepository", "SoftDeleteByFilter"))

	var model models.Session

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["_id"] = objectID
		case "parent":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["parent"] = objectID
		case "user":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["user"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.SoftDeleteDefault(ctx, r.util.DB, models.SessionCollection, bsonM)
}

func (r *SessionRepository) GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.Session, error) {
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("SessionRepository", "GetByFilter"))

	var model models.Session

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Session{}, err
			}
			bsonM["_id"] = objectID
		case "parent":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Session{}, err
			}
			bsonM["parent"] = objectID
		case "user":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Session{}, err
			}
			bsonM["user"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.First(ctx, r.util.DB, models.SessionCollection, bsonM, &model)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return internal.Session{}, nil
		}

		return internal.Session{}, err
	}

	return mapSessionToInternalData(model), nil
}

func (r *SessionRepository) GetByFilterWithOptions(ctx context.Context, filter map[string]interface{}) (internal.Session, error) {
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("SessionRepository", "GetByFilter"))

	var model models.Session

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Session{}, err
			}
			bsonM["_id"] = objectID
		case "parent":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Session{}, err
			}
			bsonM["parent"] = objectID
		case "user":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.Session{}, err
			}
			bsonM["user"] = objectID
		default:
			bsonM[key] = value
		}
	}

	opt := options.FindOne()
	opt.SetSort(bson.D{{Key: "created_at", Value: -1}})

	err := model.FirstWithOption(ctx, r.util.DB, models.SessionCollection, bsonM, opt, &model)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return internal.Session{}, nil
		}

		return internal.Session{}, err
	}

	return mapSessionToInternalData(model), nil
}

func (r *SessionRepository) SumByFilter(ctx context.Context, filter map[string]interface{}) (float64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("SessionRepository", "SumByFilter"))

	var model models.Session

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "parent":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["parent"] = objectID
		case "user":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["user"] = objectID
		default:
			bsonM[key] = value
		}
	}
	total, err := model.SumByFilter(ctx, r.util.DB, models.SessionCollection, "size", bsonM)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return 0, nil
		}

		return 0, err
	}

	return total, nil
}

func (r *SessionRepository) CountByFilter(ctx context.Context, filter map[string]interface{}) (int64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("SessionRepository", "CountByFilter"))

	var model models.Session

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "parent":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["parent"] = objectID
		case "user":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["user"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.Count(ctx, r.util.DB, models.SessionCollection, bsonM)
}

func (r *SessionRepository) GetByID(ctx context.Context, id string) (internal.Session, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("SessionRepository", "GetByID"))

	var model models.Session
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "SessionRepository.GetByID.idHex")
	}

	err = model.First(ctx, r.util.DB, models.SessionCollection, bson.M{"_id": idHex}, &model)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.Session{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.Session{}, err
	}
	return mapSessionToInternalData(model), nil
}

func (r *SessionRepository) GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.Session, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("SessionRepository", "GetAll"))
	var model models.Session
	var listModels []models.Session

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Session{}, err
			}
			bsonM["_id"] = objectID
		case "parent":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Session{}, err
			}
			bsonM["parent"] = objectID
		case "user":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Session{}, err
			}
			bsonM["user"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAll(ctx, r.util.DB, models.SessionCollection, bsonM, &listModels)
	if err != nil {
		return []internal.Session{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "SessionRepository.FindAll")
	}

	var internalModels []internal.Session

	for _, externalModel := range listModels {
		internalModel := mapSessionToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *SessionRepository) GetAllWithSoftDelete(ctx context.Context, filter map[string]interface{}) ([]internal.Session, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("SessionRepository", "GetAll"))
	var model models.Session
	var listModels []models.Session

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Session{}, err
			}
			bsonM["_id"] = objectID
		case "parent":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Session{}, err
			}
			bsonM["parent"] = objectID
		case "user":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.Session{}, err
			}
			bsonM["user"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAllWithSoftDeleteOption(ctx, r.util.DB, models.SessionCollection, bsonM, &listModels, false)
	if err != nil {
		return []internal.Session{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "SessionRepository.FindAllWithSoftDeleteOption")
	}
	var internalModels []internal.Session

	for _, externalModel := range listModels {
		internalModel := mapSessionToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *SessionRepository) GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.SessionPagin, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("SessionRepository", "GetAllWithPagination"))
	var listModels []models.Session
	var model models.Session

	resp, err := model.FindAllWithPagination(ctx, r.util.DB, models.SessionCollection, paginOpt, &listModels)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.SessionPagin{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.SessionPagin{}, err
	}

	var internalModels []internal.Session

	for _, externalModel := range listModels {
		internalModel := mapSessionToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internal.SessionPagin{
		Limit:        resp.Limit,
		Page:         resp.Page,
		Sort:         resp.Sort,
		TotalRecords: int(resp.TotalRecords),
		TotalPages:   resp.TotalPages,
		Records:      internalModels,
	}, nil
}

func mapSessionToInternalData(data models.Session) internal.Session {
	return internal.Session{
		ID:            data.ID.Hex(),
		User:          data.User.Hex(),
		Parent:        data.Parent.Hex(),
		Username:      pkg.GetStringValue(data.Username, ""),
		LoginID:       pkg.GetStringValue(data.LoginID, ""),
		Session:       pkg.GetStringValue(data.Session, ""),
		Online:        pkg.GetBoolValue(data.Online, false),
		Guest:         pkg.GetBoolValue(data.Guest, false),
		HistoricalMtc: pkg.GetStringValue(data.HistoricalMtc, ""),
		RecentMtc:     pkg.GetStringValue(data.RecentMtc, ""),
		CreatedIP:     pkg.GetStringValue(data.CreatedIP, ""),
		ValidUntil:    data.ValidUntil,
		LastAct:       data.LastAct,
		CreatedAt:     data.CreatedAt,
		UpdatedAt:     data.UpdatedAt,
	}
}

func mapSessionFromParams(params internal.CreateSession, action string) (models.Session, error) {
	fmt.Println(action)
	model := models.Session{
		Guest:         pkg.SetBoolPointer(params.Guest, action),
		Username:      pkg.FilterUsername(pkg.SetStringPointer(params.Username, action)),
		LoginID:       pkg.FilterUsername(pkg.SetStringPointer(params.LoginID, action)),
		Session:       pkg.SetStringPointer(params.Session, action),
		Online:        pkg.SetBoolPointer(params.Online, action),
		HistoricalMtc: pkg.SetStringPointer(params.HistoricalMtc, action),
		RecentMtc:     pkg.SetStringPointer(params.RecentMtc, action),
		CreatedIP:     pkg.FilterIP(pkg.SetStringPointer(params.CreatedIP, action)),
	}

	if params.User != "" {
		objectID, err := primitive.ObjectIDFromHex(params.User)
		if err != nil {
			return models.Session{}, err
		}
		model.User = objectID
	}

	if params.Parent != "" {
		objectID, err := primitive.ObjectIDFromHex(params.Parent)
		if err != nil {
			return models.Session{}, err
		}
		model.Parent = objectID
	}

	if params.ValidUntil != nil {
		validUtil := params.ValidUntil
		parsedTime, err := pkg.ParseDateString(*validUtil, pkg.DateLayoutDefault)
		if err != nil {
			return models.Session{}, err
		}

		model.ValidUntil = &parsedTime
	}

	if params.LastAct != nil {
		lastAct := params.LastAct
		parsedTime, err := pkg.ParseDateString(*lastAct, pkg.DateLayoutDefault)
		if err != nil {
			return models.Session{}, err
		}

		model.LastAct = &parsedTime
	}

	return model, nil
}
