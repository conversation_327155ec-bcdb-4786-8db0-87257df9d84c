package repositories

import (
	"context"

	"github.com/continue-team/hecarim/internal"
	"github.com/continue-team/hecarim/models"
	"github.com/continue-team/hecarim/pkg"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"
	"github.com/continue-team/riot/pkg/logger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type WebserviceRepository struct {
	util riot.Util
}

func NewWebserviceRepository(util riot.Util) *WebserviceRepository {
	return &WebserviceRepository{
		util: util,
	}
}

func (r *WebserviceRepository) Create(ctx context.Context, params internal.CreateWebService) (internal.WebService, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("WebserviceRepository.Repo", "Create"), zap.Any("params", params))

	model, err := mapWebserviceFromParams(params, "create")

	if err != nil {
		return internal.WebService{}, err
	}

	// Using WithContext to attach the provided context to the database operation
	err = model.Create(ctx, r.util.DB, models.WebServiceCollection, &model)
	if err != nil {
		return internal.WebService{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "WebserviceRepository.Create")
	}
	return mapWebserviceToInternalData(model), nil
}

func (r *WebserviceRepository) CreateBulk(ctx context.Context, params []internal.CreateWebService) ([]internal.WebService, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("WebserviceRepository.Repo", "CreateBulk"), zap.Any("params", params))

	var listModels []interface{}

	for _, param := range params {

		model, err := mapWebserviceFromParams(param, "create")

		if err != nil {
			return []internal.WebService{}, err
		}

		listModels = append(listModels, &model)
	}

	// Using WithContext to attach the provided context to the database operation
	_, err := (&models.WebService{}).CreateBulk(ctx, r.util.DB, models.WebServiceCollection, listModels)

	if err != nil {
		return []internal.WebService{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "WebserviceRepository.CreateBulk")
	}

	return []internal.WebService{}, nil
}

func (r *WebserviceRepository) Update(ctx context.Context, id string, params internal.CreateWebService) (internal.WebService, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("WebserviceRepository.Repo", "Update"))

	model, err := mapWebserviceFromParams(params, "update")

	if err != nil {
		return internal.WebService{}, err
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return internal.WebService{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "Update.ObjectIDFromHex.idHex")
	}

	model.Update(ctx, r.util.DB, models.WebServiceCollection, bson.M{"_id": objectID}, &model)

	return r.GetByID(ctx, id)
}

func (r *WebserviceRepository) UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateWebService) (internal.WebService, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("WebserviceRepository", "UpdateByFilter"))

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.WebService{}, err
			}
			bsonM["_id"] = objectID
		case "profile":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.WebService{}, err
			}
			bsonM["profile"] = objectID
		default:
			bsonM[key] = value
		}
	}

	model, err := mapWebserviceFromParams(params, "update")

	if err != nil {
		return internal.WebService{}, err
	}

	model.Update(ctx, r.util.DB, models.WebServiceCollection, bsonM, &model)

	return mapWebserviceToInternalData(model), nil
}

func (r *WebserviceRepository) SoftDelete(ctx context.Context, id string) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("WebserviceRepository", "SoftDelete"))
	var model models.WebService
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "WebserviceRepository.SoftDelete.idHex")
	}

	return model.SoftDeleteDefault(ctx, r.util.DB, models.WebServiceCollection, bson.M{"_id": idHex})
}

func (r *WebserviceRepository) SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("WebserviceRepository", "SoftDeleteByFilter"))

	var model models.WebService

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["_id"] = objectID
		case "profile":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["profile"] = objectID

		default:
			bsonM[key] = value
		}
	}

	return model.SoftBulkDelete(ctx, r.util.DB, models.WebServiceCollection, bsonM)
}

func (r *WebserviceRepository) GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.WebService, error) {
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("WebserviceRepository", "GetByFilter"))

	var model models.WebService

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.WebService{}, err
			}
			bsonM["_id"] = objectID
		case "profile":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.WebService{}, err
			}
			bsonM["profile"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.First(ctx, r.util.DB, models.WebServiceCollection, bsonM, &model)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return internal.WebService{}, nil
		}

		return internal.WebService{}, err
	}

	return mapWebserviceToInternalData(model), nil
}

func (r *WebserviceRepository) SumByFilter(ctx context.Context, filter map[string]interface{}) (float64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("WebserviceRepository", "SumByFilter"))

	var model models.WebService

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile"] = objectID
		default:
			bsonM[key] = value
		}
	}
	total, err := model.SumByFilter(ctx, r.util.DB, models.WebServiceCollection, "size", bsonM)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return 0, nil
		}

		return 0, err
	}

	return total, nil
}

func (r *WebserviceRepository) CountByFilter(ctx context.Context, filter map[string]interface{}) (int64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("WebserviceRepository", "CountByFilter"))

	var model models.WebService

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.Count(ctx, r.util.DB, models.WebServiceCollection, bsonM)
}

func (r *WebserviceRepository) GetByID(ctx context.Context, id string) (internal.WebService, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("WebserviceRepository", "GetByID"))

	var model models.WebService
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "WebserviceRepository.GetByID.idHex")
	}

	err = model.First(ctx, r.util.DB, models.WebServiceCollection, bson.M{"_id": idHex}, &model)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.WebService{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.WebService{}, err
	}
	return mapWebserviceToInternalData(model), nil
}

func (r *WebserviceRepository) GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.WebService, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("WebserviceRepository", "GetAll"))
	var model models.WebService
	var listModels []models.WebService

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.WebService{}, err
			}
			bsonM["_id"] = objectID
		case "profile":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.WebService{}, err
			}
			bsonM["profile"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAll(ctx, r.util.DB, models.WebServiceCollection, bsonM, &listModels)
	if err != nil {
		return []internal.WebService{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "WebserviceRepository.FindAll")
	}

	var internalModels []internal.WebService

	for _, externalModel := range listModels {
		internalModel := mapWebserviceToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *WebserviceRepository) GetAllWithSoftDelete(ctx context.Context, filter map[string]interface{}) ([]internal.WebService, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("WebserviceRepository", "GetAll"))
	var model models.WebService
	var listModels []models.WebService

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.WebService{}, err
			}
			bsonM["_id"] = objectID
		case "profile":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.WebService{}, err
			}
			bsonM["profile"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAllWithSoftDeleteOption(ctx, r.util.DB, models.WebServiceCollection, bsonM, &listModels, false)
	if err != nil {
		return []internal.WebService{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "WebserviceRepository.FindAllWithSoftDeleteOption")
	}
	var internalModels []internal.WebService

	for _, externalModel := range listModels {
		internalModel := mapWebserviceToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *WebserviceRepository) GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.WebServicePagin, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("WebserviceRepository", "GetAllWithPagination"))
	var listModels []models.WebService
	var model models.WebService

	resp, err := model.FindAllWithPagination(ctx, r.util.DB, models.WebServiceCollection, paginOpt, &listModels)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.WebServicePagin{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.WebServicePagin{}, err
	}

	var internalModels []internal.WebService

	for _, externalModel := range listModels {
		internalModel := mapWebserviceToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internal.WebServicePagin{
		Limit:        resp.Limit,
		Page:         resp.Page,
		Sort:         resp.Sort,
		TotalRecords: int(resp.TotalRecords),
		TotalPages:   resp.TotalPages,
		Records:      internalModels,
	}, nil
}

func mapWebserviceToInternalData(data models.WebService) internal.WebService {
	return internal.WebService{
		ID:          data.ID.Hex(),
		Profile:     data.Profile.Hex(),
		Name:        pkg.GetStringValue(data.Name, ""),
		Description: pkg.GetStringValue(data.Description, ""),
		AppID:       pkg.GetStringValue(data.AppID, ""),
		AppSecret:   pkg.GetStringValue(data.AppSecret, ""),
		Version:     pkg.GetIntValue(data.Version, 0),
		Telegram:    pkg.GetBoolValue(data.Telegram, false),
		Status:      pkg.GetBoolValue(data.Status, false),
		ValidUntil:  data.ValidUntil,
		CreatedAt:   data.CreatedAt,
		UpdatedAt:   data.UpdatedAt,
	}
}

func mapWebserviceFromParams(params internal.CreateWebService, action string) (models.WebService, error) {

	model := models.WebService{
		Name:        pkg.SetStringUpperPointer(params.Name, action),
		Description: pkg.SetStringPointer(params.Description, action),
		AppID:       pkg.SetStringPointer(params.AppID, action),
		AppSecret:   pkg.SetStringPointer(params.AppSecret, action),
		Version:     pkg.SetIntPointer(params.Version, action),
		Telegram:    pkg.SetBoolPointer(params.Telegram, action),
		Status:      pkg.SetBoolPointer(params.Status, action),
	}

	if params.Profile != "" {
		objectID, err := primitive.ObjectIDFromHex(params.Profile)
		if err != nil {
			return models.WebService{}, err
		}
		model.Profile = objectID
	}

	if params.ValidUntil != nil {
		validUtil := params.ValidUntil
		parsedTime, err := pkg.ParseDateString(*validUtil, pkg.DateLayoutDefault)
		if err != nil {
			return models.WebService{}, err
		}

		model.ValidUntil = &parsedTime
	}

	return model, nil
}
