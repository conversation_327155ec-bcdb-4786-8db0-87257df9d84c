package repositories

import (
	"context"

	"github.com/continue-team/hecarim/internal"
	"github.com/continue-team/hecarim/models"
	"github.com/continue-team/hecarim/pkg"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"
	"github.com/continue-team/riot/pkg/logger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type SocialProviderRepository struct {
	util riot.Util
}

func NewSocialProviderRepository(util riot.Util) *SocialProviderRepository {
	return &SocialProviderRepository{
		util: util,
	}
}

func (r *SocialProviderRepository) Create(ctx context.Context, params internal.CreateSocialProvider) (internal.SocialProvider, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("SocialProviderRepository.Repo", "Create"), zap.Any("params", params))

	model, err := mapSocialProviderFromParams(params, "create")

	if err != nil {
		return internal.SocialProvider{}, err
	}

	// Using WithContext to attach the provided context to the database operation
	err = model.Create(ctx, r.util.DB, models.SocialProviderCollection, &model)
	if err != nil {
		return internal.SocialProvider{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "SocialProviderRepository.Create")
	}
	return mapSocialProviderToInternalData(model), nil
}

func (r *SocialProviderRepository) CreateBulk(ctx context.Context, params []internal.CreateSocialProvider) ([]internal.SocialProvider, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("SocialProviderRepository.Repo", "CreateBulk"), zap.Any("params", params))

	var listModels []interface{}

	for _, param := range params {

		model, err := mapSocialProviderFromParams(param, "create")

		if err != nil {
			return []internal.SocialProvider{}, err
		}

		listModels = append(listModels, &model)
	}

	// Using WithContext to attach the provided context to the database operation
	_, err := (&models.SocialProvider{}).CreateBulk(ctx, r.util.DB, models.SocialProviderCollection, listModels)

	if err != nil {
		return []internal.SocialProvider{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "SocialProviderRepository.CreateBulk")
	}

	return []internal.SocialProvider{}, nil
}

func (r *SocialProviderRepository) Update(ctx context.Context, id string, params internal.CreateSocialProvider) (internal.SocialProvider, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("SocialProviderRepository.Repo", "Update"))

	model, err := mapSocialProviderFromParams(params, "update")

	if err != nil {
		return internal.SocialProvider{}, err
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return internal.SocialProvider{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "Update.ObjectIDFromHex.idHex")
	}

	model.Update(ctx, r.util.DB, models.SocialProviderCollection, bson.M{"_id": objectID}, &model)

	return r.GetByID(ctx, id)
}

func (r *SocialProviderRepository) UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateSocialProvider) (internal.SocialProvider, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("SocialProviderRepository", "UpdateByFilter"))

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.SocialProvider{}, err
			}
			bsonM["_id"] = objectID
		case "profile":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.SocialProvider{}, err
			}
			bsonM["profile"] = objectID
		default:
			bsonM[key] = value
		}
	}

	model, err := mapSocialProviderFromParams(params, "update")

	if err != nil {
		return internal.SocialProvider{}, err
	}

	model.Update(ctx, r.util.DB, models.SocialProviderCollection, bsonM, &model)

	return mapSocialProviderToInternalData(model), nil
}

func (r *SocialProviderRepository) SoftDelete(ctx context.Context, id string) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("SocialProviderRepository", "SoftDelete"))
	var model models.SocialProvider
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "SocialProviderRepository.SoftDelete.idHex")
	}

	return model.SoftDeleteDefault(ctx, r.util.DB, models.SocialProviderCollection, bson.M{"_id": idHex})
}

func (r *SocialProviderRepository) SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("SocialProviderRepository", "SoftDeleteByFilter"))

	var model models.SocialProvider

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["_id"] = objectID
		case "profile":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["profile"] = objectID

		default:
			bsonM[key] = value
		}
	}

	return model.SoftBulkDelete(ctx, r.util.DB, models.SocialProviderCollection, bsonM)
}

func (r *SocialProviderRepository) GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.SocialProvider, error) {
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("SocialProviderRepository", "GetByFilter"))

	var model models.SocialProvider

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.SocialProvider{}, err
			}
			bsonM["_id"] = objectID
		case "profile":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.SocialProvider{}, err
			}
			bsonM["profile"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.First(ctx, r.util.DB, models.SocialProviderCollection, bsonM, &model)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return internal.SocialProvider{}, nil
		}

		return internal.SocialProvider{}, err
	}

	return mapSocialProviderToInternalData(model), nil
}

func (r *SocialProviderRepository) SumByFilter(ctx context.Context, filter map[string]interface{}) (float64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("SocialProviderRepository", "SumByFilter"))

	var model models.SocialProvider

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile"] = objectID
		default:
			bsonM[key] = value
		}
	}
	total, err := model.SumByFilter(ctx, r.util.DB, models.SocialProviderCollection, "size", bsonM)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return 0, nil
		}

		return 0, err
	}

	return total, nil
}

func (r *SocialProviderRepository) CountByFilter(ctx context.Context, filter map[string]interface{}) (int64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("SocialProviderRepository", "CountByFilter"))

	var model models.SocialProvider

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.Count(ctx, r.util.DB, models.SocialProviderCollection, bsonM)
}

func (r *SocialProviderRepository) GetByID(ctx context.Context, id string) (internal.SocialProvider, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("SocialProviderRepository", "GetByID"))

	var model models.SocialProvider
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "SocialProviderRepository.GetByID.idHex")
	}

	err = model.First(ctx, r.util.DB, models.SocialProviderCollection, bson.M{"_id": idHex}, &model)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.SocialProvider{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.SocialProvider{}, err
	}
	return mapSocialProviderToInternalData(model), nil
}

func (r *SocialProviderRepository) GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.SocialProvider, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("SocialProviderRepository", "GetAll"))
	var model models.SocialProvider
	var listModels []models.SocialProvider

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.SocialProvider{}, err
			}
			bsonM["_id"] = objectID
		case "profile":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.SocialProvider{}, err
			}
			bsonM["profile"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAll(ctx, r.util.DB, models.SocialProviderCollection, bsonM, &listModels)
	if err != nil {
		return []internal.SocialProvider{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "SocialProviderRepository.FindAll")
	}

	var internalModels []internal.SocialProvider

	for _, externalModel := range listModels {
		internalModel := mapSocialProviderToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *SocialProviderRepository) GetAllWithSoftDelete(ctx context.Context, filter map[string]interface{}) ([]internal.SocialProvider, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("SocialProviderRepository", "GetAll"))
	var model models.SocialProvider
	var listModels []models.SocialProvider

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.SocialProvider{}, err
			}
			bsonM["_id"] = objectID
		case "profile":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.SocialProvider{}, err
			}
			bsonM["profile"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAllWithSoftDeleteOption(ctx, r.util.DB, models.SocialProviderCollection, bsonM, &listModels, false)
	if err != nil {
		return []internal.SocialProvider{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "SocialProviderRepository.FindAllWithSoftDeleteOption")
	}
	var internalModels []internal.SocialProvider

	for _, externalModel := range listModels {
		internalModel := mapSocialProviderToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *SocialProviderRepository) GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.SocialProviderPagin, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("SocialProviderRepository", "GetAllWithPagination"))
	var listModels []models.SocialProvider
	var model models.SocialProvider

	resp, err := model.FindAllWithPagination(ctx, r.util.DB, models.SocialProviderCollection, paginOpt, &listModels)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.SocialProviderPagin{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.SocialProviderPagin{}, err
	}

	var internalModels []internal.SocialProvider

	for _, externalModel := range listModels {
		internalModel := mapSocialProviderToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internal.SocialProviderPagin{
		Limit:        resp.Limit,
		Page:         resp.Page,
		Sort:         resp.Sort,
		TotalRecords: int(resp.TotalRecords),
		TotalPages:   resp.TotalPages,
		Records:      internalModels,
	}, nil
}

func mapSocialProviderToInternalData(data models.SocialProvider) internal.SocialProvider {
	return internal.SocialProvider{
		ID:              data.ID.Hex(),
		Profile:         data.Profile.Hex(),
		Name:            pkg.GetStringValue(data.Name, ""),
		Description:     pkg.GetStringValue(data.Description, ""),
		AuthKey:         pkg.GetStringValue(data.AuthKey, ""),
		AuthSecret:      pkg.GetStringValue(data.AuthSecret, ""),
		AuthCallback:    pkg.GetStringValue(data.AuthCallback, ""),
		ConnectCallback: pkg.GetStringValue(data.ConnectCallback, ""),
		AuthScopes:      pkg.GetStringValue(data.AuthScopes, ""),
		Version:         pkg.GetIntValue(data.Version, 0),
		Status:          pkg.GetBoolValue(data.Status, false),
		CreatedAt:       data.CreatedAt,
		UpdatedAt:       data.UpdatedAt,
	}
}

func mapSocialProviderFromParams(params internal.CreateSocialProvider, action string) (models.SocialProvider, error) {

	model := models.SocialProvider{
		Name:            pkg.SetStringPointer(params.Name, action),
		Description:     pkg.SetStringPointer(params.Description, action),
		AuthKey:         pkg.SetStringPointer(params.AuthKey, action),
		AuthSecret:      pkg.SetStringPointer(params.AuthSecret, action),
		AuthCallback:    pkg.SetStringPointer(params.AuthCallback, action),
		ConnectCallback: pkg.SetStringPointer(params.ConnectCallback, action),
		AuthScopes:      pkg.SetStringPointer(params.AuthScopes, action),
		Version:         pkg.SetIntPointer(params.Version, action),
		Status:          pkg.SetBoolPointer(params.Status, action),
	}

	if params.Profile != "" {
		objectID, err := primitive.ObjectIDFromHex(params.Profile)
		if err != nil {
			return models.SocialProvider{}, err
		}
		model.Profile = objectID
	}

	return model, nil
}
