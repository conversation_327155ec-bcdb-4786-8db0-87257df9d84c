package repositories

import (
	"context"

	"github.com/continue-team/hecarim/internal"
	"github.com/continue-team/hecarim/models"
	"github.com/continue-team/hecarim/pkg"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"
	"github.com/continue-team/riot/pkg/logger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type UserSocialProviderRepository struct {
	util riot.Util
}

func NewUserSocialProviderRepository(util riot.Util) *UserSocialProviderRepository {
	return &UserSocialProviderRepository{
		util: util,
	}
}

func (r *UserSocialProviderRepository) Create(ctx context.Context, params internal.CreateUserSocialProvider) (internal.UserSocialProvider, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("UserSocialProviderRepository.Repo", "Create"), zap.Any("params", params))

	model, err := mapUserSocialProviderFromParams(params, "create")

	if err != nil {
		return internal.UserSocialProvider{}, err
	}

	// Using WithContext to attach the provided context to the database operation
	err = model.Create(ctx, r.util.DB, models.UserSocialProviderCollection, &model)
	if err != nil {
		return internal.UserSocialProvider{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "UserSocialProviderRepository.Create")
	}
	return mapUserSocialProviderToInternalData(model), nil
}

func (r *UserSocialProviderRepository) CreateBulk(ctx context.Context, params []internal.CreateUserSocialProvider) ([]internal.UserSocialProvider, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("UserSocialProviderRepository.Repo", "CreateBulk"), zap.Any("params", params))

	var listModels []interface{}

	for _, param := range params {

		model, err := mapUserSocialProviderFromParams(param, "create")

		if err != nil {
			return []internal.UserSocialProvider{}, err
		}

		listModels = append(listModels, &model)
	}

	// Using WithContext to attach the provided context to the database operation
	_, err := (&models.UserSocialProvider{}).CreateBulk(ctx, r.util.DB, models.UserSocialProviderCollection, listModels)

	if err != nil {
		return []internal.UserSocialProvider{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "UserSocialProviderRepository.CreateBulk")
	}

	return []internal.UserSocialProvider{}, nil
}

func (r *UserSocialProviderRepository) Update(ctx context.Context, id string, params internal.CreateUserSocialProvider) (internal.UserSocialProvider, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("UserSocialProviderRepository.Repo", "Update"))

	model, err := mapUserSocialProviderFromParams(params, "update")

	if err != nil {
		return internal.UserSocialProvider{}, err
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return internal.UserSocialProvider{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "Update.ObjectIDFromHex.idHex")
	}

	model.Update(ctx, r.util.DB, models.UserSocialProviderCollection, bson.M{"_id": objectID}, &model)

	return r.GetByID(ctx, id)
}

func (r *UserSocialProviderRepository) UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateUserSocialProvider) (internal.UserSocialProvider, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("UserSocialProviderRepository", "UpdateByFilter"))

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.UserSocialProvider{}, err
			}
			bsonM["_id"] = objectID
		case "profile":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.UserSocialProvider{}, err
			}
			bsonM["profile"] = objectID
		case "user":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.UserSocialProvider{}, err
			}
			bsonM["user"] = objectID
		case "parent":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.UserSocialProvider{}, err
			}
			bsonM["parent"] = objectID
		default:
			bsonM[key] = value
		}
	}

	model, err := mapUserSocialProviderFromParams(params, "update")

	if err != nil {
		return internal.UserSocialProvider{}, err
	}

	model.Update(ctx, r.util.DB, models.UserSocialProviderCollection, bsonM, &model)

	return mapUserSocialProviderToInternalData(model), nil
}

func (r *UserSocialProviderRepository) SoftDelete(ctx context.Context, id string) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("UserSocialProviderRepository", "SoftDelete"))
	var model models.UserSocialProvider
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserSocialProviderRepository.SoftDelete.idHex")
	}

	return model.SoftDeleteDefault(ctx, r.util.DB, models.UserSocialProviderCollection, bson.M{"_id": idHex})
}

func (r *UserSocialProviderRepository) SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("UserSocialProviderRepository", "SoftDeleteByFilter"))

	var model models.UserSocialProvider

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["_id"] = objectID
		case "profile":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["profile"] = objectID
		case "user":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["user"] = objectID
		case "parent":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return err
			}
			bsonM["parent"] = objectID

		default:
			bsonM[key] = value
		}
	}

	return model.SoftBulkDelete(ctx, r.util.DB, models.UserSocialProviderCollection, bsonM)
}

func (r *UserSocialProviderRepository) GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.UserSocialProvider, error) {
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("UserSocialProviderRepository", "GetByFilter"))

	var model models.UserSocialProvider

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.UserSocialProvider{}, err
			}
			bsonM["_id"] = objectID
		case "profile":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.UserSocialProvider{}, err
			}
			bsonM["profile"] = objectID
		case "user":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.UserSocialProvider{}, err
			}
			bsonM["user"] = objectID
		case "parent":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return internal.UserSocialProvider{}, err
			}
			bsonM["parent"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.First(ctx, r.util.DB, models.UserSocialProviderCollection, bsonM, &model)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return internal.UserSocialProvider{}, nil
		}

		return internal.UserSocialProvider{}, err
	}

	return mapUserSocialProviderToInternalData(model), nil
}

func (r *UserSocialProviderRepository) SumByFilter(ctx context.Context, filter map[string]interface{}) (float64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("UserSocialProviderRepository", "SumByFilter"))

	var model models.UserSocialProvider

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile"] = objectID
		case "user":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["user"] = objectID
		case "parent":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["parent"] = objectID
		default:
			bsonM[key] = value
		}
	}
	total, err := model.SumByFilter(ctx, r.util.DB, models.UserSocialProviderCollection, "size", bsonM)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return 0, nil
		}

		return 0, err
	}

	return total, nil
}

func (r *UserSocialProviderRepository) CountByFilter(ctx context.Context, filter map[string]interface{}) (int64, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("UserSocialProviderRepository", "CountByFilter"))

	var model models.UserSocialProvider

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["_id"] = objectID
		case "profile":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["profile"] = objectID
		case "user":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["user"] = objectID
		case "parent":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return 0, err
			}
			bsonM["parent"] = objectID
		default:
			bsonM[key] = value
		}
	}

	return model.Count(ctx, r.util.DB, models.UserSocialProviderCollection, bsonM)
}

func (r *UserSocialProviderRepository) GetByID(ctx context.Context, id string) (internal.UserSocialProvider, error) {

	r.util.Logger = logger.FromCtx(ctx).With(zap.String("UserSocialProviderRepository", "GetByID"))

	var model models.UserSocialProvider
	idHex, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserSocialProviderRepository.GetByID.idHex")
	}

	err = model.First(ctx, r.util.DB, models.UserSocialProviderCollection, bson.M{"_id": idHex}, &model)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.UserSocialProvider{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.UserSocialProvider{}, err
	}
	return mapUserSocialProviderToInternalData(model), nil
}

func (r *UserSocialProviderRepository) GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.UserSocialProvider, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("UserSocialProviderRepository", "GetAll"))
	var model models.UserSocialProvider
	var listModels []models.UserSocialProvider

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.UserSocialProvider{}, err
			}
			bsonM["_id"] = objectID
		case "profile":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.UserSocialProvider{}, err
			}
			bsonM["profile"] = objectID
		case "user":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.UserSocialProvider{}, err
			}
			bsonM["user"] = objectID
		case "parent":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.UserSocialProvider{}, err
			}
			bsonM["parent"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAll(ctx, r.util.DB, models.UserSocialProviderCollection, bsonM, &listModels)
	if err != nil {
		return []internal.UserSocialProvider{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "UserSocialProviderRepository.FindAll")
	}

	var internalModels []internal.UserSocialProvider

	for _, externalModel := range listModels {
		internalModel := mapUserSocialProviderToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *UserSocialProviderRepository) GetAllWithSoftDelete(ctx context.Context, filter map[string]interface{}) ([]internal.UserSocialProvider, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("UserSocialProviderRepository", "GetAll"))
	var model models.UserSocialProvider
	var listModels []models.UserSocialProvider

	bsonM := make(bson.M)
	for key, value := range filter {
		switch key {
		case "id":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.UserSocialProvider{}, err
			}
			bsonM["_id"] = objectID
		case "profile":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.UserSocialProvider{}, err
			}
			bsonM["profile"] = objectID
		case "user":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.UserSocialProvider{}, err
			}
			bsonM["user"] = objectID
		case "parent":
			objectID, err := primitive.ObjectIDFromHex(value.(string))
			if err != nil {
				return []internal.UserSocialProvider{}, err
			}
			bsonM["parent"] = objectID
		default:
			bsonM[key] = value
		}
	}

	err := model.FindAllWithSoftDeleteOption(ctx, r.util.DB, models.UserSocialProviderCollection, bsonM, &listModels, false)
	if err != nil {
		return []internal.UserSocialProvider{}, riot.WrapErrorfLog(r.util.Logger, err, riot.ErrorCodeUnknown, "UserSocialProviderRepository.FindAllWithSoftDeleteOption")
	}
	var internalModels []internal.UserSocialProvider

	for _, externalModel := range listModels {
		internalModel := mapUserSocialProviderToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internalModels, nil
}

func (r *UserSocialProviderRepository) GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.UserSocialProviderPagin, error) {
	// Define logger with correlation ID
	r.util.Logger = logger.FromCtx(ctx).With(zap.String("UserSocialProviderRepository", "GetAllWithPagination"))
	var listModels []models.UserSocialProvider
	var model models.UserSocialProvider

	resp, err := model.FindAllWithPagination(ctx, r.util.DB, models.UserSocialProviderCollection, paginOpt, &listModels)
	if err != nil {
		// If the error is due to not finding any documents, return empty
		if err == mongo.ErrNoDocuments {
			return internal.UserSocialProviderPagin{}, nil
		}

		// If there is an error other than not finding documents, return the error
		return internal.UserSocialProviderPagin{}, err
	}

	var internalModels []internal.UserSocialProvider

	for _, externalModel := range listModels {
		internalModel := mapUserSocialProviderToInternalData(externalModel)
		internalModels = append(internalModels, internalModel)
	}

	return internal.UserSocialProviderPagin{
		Limit:        resp.Limit,
		Page:         resp.Page,
		Sort:         resp.Sort,
		TotalRecords: int(resp.TotalRecords),
		TotalPages:   resp.TotalPages,
		Records:      internalModels,
	}, nil
}

func mapUserSocialProviderToInternalData(data models.UserSocialProvider) internal.UserSocialProvider {
	return internal.UserSocialProvider{
		ID:          data.ID.Hex(),
		User:        data.User.Hex(),
		Parent:      data.Parent.Hex(),
		Alias:       pkg.GetStringValue(data.Alias, ""),
		LoginID:     pkg.GetStringValue(data.LoginID, ""),
		Provider:    pkg.GetStringValue(data.Provider, ""),
		Email:       pkg.GetStringValue(data.Email, ""),
		Name:        pkg.GetStringValue(data.Name, ""),
		FirstName:   pkg.GetStringValue(data.FirstName, ""),
		LastName:    pkg.GetStringValue(data.LastName, ""),
		NickName:    pkg.GetStringValue(data.NickName, ""),
		Description: pkg.GetStringValue(data.Description, ""),
		UserID:      pkg.GetStringValue(data.UserID, ""),
		AvatarURL:   pkg.GetStringValue(data.AvatarURL, ""),
		Location:    pkg.GetStringValue(data.Location, ""),
		WhitelistIP: pkg.GetStringValue(data.WhitelistIP, ""),
		CreatedIP:   pkg.GetStringValue(data.CreatedIP, ""),
		CreatedAt:   data.CreatedAt,
		UpdatedAt:   data.UpdatedAt,
	}
}

func mapUserSocialProviderFromParams(params internal.CreateUserSocialProvider, action string) (models.UserSocialProvider, error) {

	model := models.UserSocialProvider{
		LoginID:     pkg.SetStringPointer(params.LoginID, action),
		Alias:       pkg.SetStringPointer(params.Alias, action),
		Provider:    pkg.SetStringPointer(params.Provider, action),
		Email:       pkg.SetStringPointer(params.Email, action),
		Name:        pkg.SetStringPointer(params.Name, action),
		FirstName:   pkg.SetStringPointer(params.FirstName, action),
		LastName:    pkg.SetStringPointer(params.LastName, action),
		NickName:    pkg.SetStringPointer(params.NickName, action),
		Description: pkg.SetStringPointer(params.Description, action),
		UserID:      pkg.SetStringPointer(params.UserID, action),
		AvatarURL:   pkg.SetStringPointer(params.AvatarURL, action),
		Location:    pkg.SetStringPointer(params.Location, action),
		WhitelistIP: pkg.SetStringPointer(params.WhitelistIP, action),
		CreatedIP:   pkg.SetStringPointer(params.CreatedIP, action),
	}

	if params.User != "" {
		objectID, err := primitive.ObjectIDFromHex(params.User)
		if err != nil {
			return models.UserSocialProvider{}, err
		}
		model.User = objectID
	}

	if params.Parent != "" {
		objectID, err := primitive.ObjectIDFromHex(params.Parent)
		if err != nil {
			return models.UserSocialProvider{}, err
		}
		model.Parent = objectID
	}

	return model, nil
}
