package services

import (
	"context"
	"errors"
	"fmt"
	"net/url"
	"strings"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"

	"github.com/continue-team/hecarim/internal"
	"github.com/continue-team/hecarim/internal/rabbitmq"
	"github.com/continue-team/hecarim/internal/repositories"
	"github.com/continue-team/hecarim/internal/request"
	"github.com/continue-team/hecarim/pkg"
	"github.com/continue-team/riot/pkg/logger"
	"github.com/mercari/go-circuitbreaker"
	"go.uber.org/zap"
)

type UserRepository interface {
	Create(ctx context.Context, params internal.CreateUser) (internal.User, error)
	SoftDelete(ctx context.Context, id string) error
	Update(ctx context.Context, id string, params internal.CreateUser) (internal.User, error)
	GetByID(ctx context.Context, id string) (internal.User, error)
	GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.User, error)
	GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.UserPagin, error)
	GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.User, error)
	CountByFilter(ctx context.Context, filter map[string]interface{}) (int64, error)
	UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateUser) (internal.User, error)
}

type UserService struct {
	repo                   UserRepository
	sessionRepo            SessionRepository
	userSocialProviderRepo *repositories.UserSocialProviderRepository
	userSecuritySvc        *UserSecurityService
	sessionSvc             *SessionService
	referrerCodeSvc        *ReferrerCodeService
	publisher              *rabbitmq.Publisher
	cb                     *circuitbreaker.CircuitBreaker
	util                   riot.Util
}

func NewUserService(util riot.Util, repo UserRepository, sessionRepo SessionRepository, userSocialProviderRepo *repositories.UserSocialProviderRepository, userSecuritySvc *UserSecurityService, sessionSvc *SessionService, referrerCodeSvc *ReferrerCodeService, publisher *rabbitmq.Publisher) *UserService {
	return &UserService{
		repo:                   repo,
		sessionRepo:            sessionRepo,
		userSocialProviderRepo: userSocialProviderRepo,
		userSecuritySvc:        userSecuritySvc,
		sessionSvc:             sessionSvc,
		referrerCodeSvc:        referrerCodeSvc,
		publisher:              publisher,
		util:                   util,
		cb: circuitbreaker.New(
			circuitbreaker.WithOpenTimeout(time.Second*30),
			circuitbreaker.WithTripFunc(circuitbreaker.NewTripFuncConsecutiveFailures(3)),
			circuitbreaker.WithOnStateChangeHookFn(func(oldState, newState circuitbreaker.State) {
				util.Logger.Info("state changed",
					zap.String("oldState", string(oldState)),
					zap.String("newState", string(newState)),
				)
			}),
		),
	}
}

func (s *UserService) Create(ctx context.Context, identifier internal.Identifier, params internal.CreateUser, nexusData riot.Nexus) (internal.DataProfile, error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("UserService", "Create"))

	var parent internal.ParentProfile
	validReferrer := false
	parentReset := false

	webservice := riot.GetNexusWebservice(nexusData)
	profile := riot.GetNexusProfile(nexusData)

	var role int

	if profile.ID != "" {
		parent = internal.ParentProfile{
			ParentID: profile.ParentID,
			ID:       profile.ID,
			Username: profile.Username,
			LoginID:  profile.LoginID,
			Role:     profile.Role,
		}

		parentReset = true

	} else {
		parent = internal.ParentProfile{
			ParentID: webservice.ParentID,
			ID:       webservice.ID,
			Username: webservice.Username,
			LoginID:  webservice.LoginID,
			Role:     webservice.Role,
		}
	}

	switch parent.Role {
	case riot.RoleCompany:
		role = riot.RoleCustomer

		if params.IsGroup {
			role = riot.RoleGroup
		}

	case riot.RoleCorporateAdm:
		role = riot.RoleCorporate
	default:
		role = parent.Role + 1
	}

	if parent.Role > 6 {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, errors.New("not allow"), riot.ErrorCodeInvalidArgument, "UserService.NotAllow")
	}

	//check if reff is valid
	if params.ReferrerCode != nil && *params.ReferrerCode != "" {
		filterReferrer := map[string]interface{}{
			"parent_id": parent.ID,
			"login_id":  strings.ToUpper(*params.ReferrerCode),
			"status":    true,
		}

		referrer, err := s.repo.GetByFilter(ctx, filterReferrer)

		s.util.Logger.Info("UserService.Create.Referrer", zap.Any("Info", referrer))

		if err != nil {
			return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserService.s.repo.GetByFilter")
		}

		params.ReferrerID = &referrer.ID
	}

	if params.MarketingCode != nil && *params.MarketingCode != "" {
		filterMarketing := map[string]interface{}{
			"parent_id": parent.ID,
			"login_id":  strings.ToUpper(*params.MarketingCode),
			"status":    true,
		}

		marketing, err := s.repo.GetByFilter(ctx, filterMarketing)

		s.util.Logger.Info("UserService.Create.Marketing", zap.Any("Info", marketing))

		if err != nil {
			return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserService.s.repo.GetByFilter")
		}

		params.MarketingID = &marketing.ID
	}

	if params.VoucherCode != nil && *params.VoucherCode != "" {

		identifierRequest := request.Identifier{
			AccessToken: identifier.AuthToken,
		}

		newRequest := request.NewRequest(s.util, s.util.Conf.Get("GATEWAY_URL"))

		respVoucher, err := newRequest.ValidateVoucher(ctx, identifierRequest, *params.VoucherCode, internal.QueryObject{})
		if err != nil {
			return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserService.s.repo.GetByFilter")
		}

		if err := params.ValidateVoucher(respVoucher.Code); err != nil {
			return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, errors.New("invalid voucher"), riot.ErrorCodeInvalidArgument, "UserService.Create.newRequest.ValidateVoucher")
		}

		params.VoucherID = &respVoucher.Data.ID
	}

	if params.Username == nil && params.LoginID != nil {
		params.Username = params.LoginID
	}

	//find parent profile
	filterParent := map[string]interface{}{
		"id": parent.ID,
	}

	actor, err := s.repo.GetByFilter(ctx, filterParent)

	if err != nil {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UserService.s.repo.GetByFilter")
	}

	if err := actor.ValidateParentNotExist(); err != nil {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserService.Profile.NotExist")
	}

	if err := actor.NotActive(); err != nil {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserService.Profile.NotActive")
	}

	//register user

	// check username already exists
	usernameIsExist, err := s.CheckDuplicateUsernameByParentID(ctx, parent.ID, params.Username)

	if err != nil {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UserService.CheckDuplicateUsernameByParentID")
	}

	if err := usernameIsExist.ValidateExist(); err != nil {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserService.CheckDuplicateUsernameByParentID")
	}

	//check phone number already exist if role as customer.
	if (params.MobilePhone != nil && *params.MobilePhone != "") && role == riot.RoleCustomer {

		//check mobile phone already exists
		phoneIsExist, err := s.CheckDuplicatePhoneByParentID(ctx, parent.ID, params.MobilePhone)

		if err != nil {
			return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UserService.CheckDuplicatePhoneByParentID")
		}

		if err := phoneIsExist.ValidatePhoneExist(); err != nil {
			return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserService.CheckDuplicatePhoneByParentID")
		}

	}

	//check email already exist if role as customer.
	if (params.Email != nil && *params.Email != "") && role == riot.RoleCustomer {

		//check mobile phone already exists
		emailIsExist, err := s.CheckDuplicateEmailByParentID(ctx, parent.ID, params.Email)

		if err != nil {
			return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UserService.CheckDuplicateEmailByParentID")
		}

		if err := emailIsExist.ValidateEmailExist(); err != nil {
			return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserService.CheckDuplicateEmailByParentID")
		}

	}

	//check referrer code
	// if params.ReferrerCode != nil && *params.ReferrerCode != "" {

	// 	nexusData.Server.Profile = (*riot.NexusProfile)(&parent)
	// 	validateReffCode, err := s.referrerCodeSvc.ValidateCode(ctx, *params.ReferrerCode, nexusData)

	// 	if err != nil {
	// 		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserService.referrerCodeSvc.ValidateCode")
	// 	}

	// 	if err := validateReffCode.CheckValidate(); err != nil {
	// 		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserService.referrerCodeSvc.ValidateCode")
	// 	}

	// 	validReferrer = true
	// }

	if err := params.ValidateUsernameLength(role, parent.Username, validReferrer); err != nil {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserService.params.ValidateUsernameLength")
	}

	//define profile
	now := time.Now()
	mobilePhoneCode := s.util.Conf.Get("MOBILE_PHONE_CODE")
	mobilePhoneCountry := s.util.Conf.Get("MOBILE_PHONE_COUNTRY")
	status := true

	params.ParentID = parent.ID
	params.ParentRole = &parent.Role
	params.ParentUsername = &parent.Username
	params.Role = &role
	params.LoginID = params.Username
	params.ParentUsername = &parent.Username
	params.LastLoggedIn = &now
	params.LastLoggedInIP = params.CreatedIP
	params.MobilePhoneCode = &mobilePhoneCode
	params.MobilePhoneCountry = &mobilePhoneCountry
	params.Status = &status

	//generate username
	if parent.Role == riot.RoleCompany && parent.Role != riot.RoleSubaccount {
		filter := map[string]interface{}{
			"parent_id": parent.ID,
		}

		//get total user
		totalUser, err := s.repo.CountByFilter(ctx, filter)

		if err != nil {
			return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UserService.s.repo.CountByFilter")
		}

		for {
			totalUser++
			totalUserStr := fmt.Sprintf("%d", totalUser)
			params.Username = &totalUserStr

			// Ensure the username is 11 characters long, padding with leading zeros if necessary
			for len(*params.Username) < 11 {
				*params.Username = "0" + *params.Username
			}

			// Prepend parent's username to the generated username
			username := actor.Username + *params.Username
			*params.Username = username

			// Check if the username already exists
			isExist, err := s.CheckDuplicateUsernameByParentID(ctx, parent.ID, params.Username)
			if err != nil {
				return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UserService.CheckDuplicateUsernameByParentID")
			}

			// Validate if the username exists or not
			if err := isExist.ValidateNotExist(); err != nil {
				// If it doesn't exist, exit the loop
				break
			}
		}

	}

	if err := params.Validate(); err != nil {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserService.params.Validate")
	}

	user, err := s.repo.Create(ctx, params)
	if err != nil {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UserService.s.repo.Create")
	}

	// create user security
	whitelistIP := "*"
	userSecurityParams := internal.CreateUserSecurity{
		User:        user.ID,
		LoginID:     &user.LoginID,
		Alias:       &user.LoginID,
		Password:    &params.Password,
		Pin:         &params.Pin,
		WhitelistIP: &whitelistIP,
		CreatedIP:   params.CreatedIP,
	}

	if parentReset {
		isReset := true
		userSecurityParams.ResetLoginID = &isReset
		userSecurityParams.ResetPassword = &isReset
		userSecurityParams.ResetPin = &isReset
	} else {
		isReset := false
		userSecurityParams.ResetLoginID = &isReset
		userSecurityParams.ResetPassword = &isReset
		userSecurityParams.ResetPin = &isReset
	}

	//create user security
	security, err := s.userSecuritySvc.Create(ctx, userSecurityParams, nexusData)

	if err != nil {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UserService.userSecuritySvc.Create")
	}

	sessionParams := internal.CreateSession{
		Parent:    user.ParentID,
		User:      user.ID,
		Username:  &user.Username,
		LoginID:   &user.LoginID,
		CreatedIP: params.CreatedIP,
	}

	//create session
	session, err := s.sessionSvc.Create(ctx, sessionParams, user, nexusData)

	if err != nil {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UserService.sessionSvc.Create")
	}

	//check if marchant
	if user.Role == riot.RoleCustomer {
		//create merchant
		if params.CreateMerchant != nil && *params.CreateMerchant {
			//publish event

			payload := internal.PayloadCreateMerchant{
				Username:    user.Username,
				LoginID:     user.LoginID,
				ProfileID:   user.ID,
				CompanyID:   parent.ID,
				ReferrerID:  user.ReferrerID,
				MarketingID: user.MarketingID,
				VoucherID:   user.VoucherID,
			}

			if err := s.publisher.PublishCreateMerchant(ctx, payload); err != nil {
				return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UserService.publisher.PublishCreateMerchant")
			}
		}
	}

	return internal.DataProfile{
		Profile: user,
		Security: internal.UserSecurityProtected{
			ID:            security.ID,
			User:          security.User,
			LoginID:       security.LoginID,
			Alias:         security.Alias,
			ResetLoginID:  security.ResetLoginID,
			ResetPassword: security.ResetPassword,
			Pin:           security.Pin,
			ResetPin:      security.ResetPin,
			WhitelistIP:   security.WhitelistIP,
			CreatedIP:     security.CreatedIP,
			CreatedAt:     security.CreatedAt,
			UpdatedAt:     security.UpdatedAt,
		},
		Session: session,
	}, nil
}

func (s *UserService) Update(ctx context.Context, identifier internal.Identifier, id string, params internal.CreateUser, nexusData riot.Nexus) (_ internal.DataProfile, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("UserService", "Update"))

	if !s.cb.Ready() {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	// webservice := riot.GetNexusWebservice(nexusData)
	profile := riot.GetNexusProfile(nexusData)

	if params.Username != nil && *params.Username != "" {
		params.LoginID = params.Username
	}

	paramsUpdate := internal.CreateUser{
		FirstName:           params.FirstName,
		LastName:            params.LastName,
		LoginID:             params.LoginID,
		Email:               params.Email,
		Alias:               params.Alias,
		NickName:            params.NickName,
		MobilePhone:         params.MobilePhone,
		MobilePhoneCountry:  params.MobilePhoneCountry,
		MobilePhoneCode:     params.MobilePhoneCode,
		MobilePhoneNumber:   params.MobilePhoneNumber,
		MobilePhoneVerified: params.MobilePhoneVerified,
		Gender:              params.Gender,
		Type:                params.Type,
		Avatar:              params.Avatar,
		Group:               params.Group,
		Bookmark:            params.Bookmark,
		Bank:                params.Bank,
		Mtc:                 params.Mtc,
		AccountNumber:       params.AccountNumber,
		AccountName:         params.AccountName,
		ReferrerCode:        params.ReferrerCode,
	}

	//check if reff is valid
	if params.ReferrerCode != nil && *params.ReferrerCode != "" {
		filterReferrer := map[string]interface{}{
			"parent_id": profile.ParentID,
			"login_id":  strings.ToUpper(*params.ReferrerCode),
			"status":    true,
		}

		referrer, err := s.repo.GetByFilter(ctx, filterReferrer)

		s.util.Logger.Info("UserService.Create.Referrer", zap.Any("Info", referrer))

		if err != nil {
			return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserService.s.repo.GetByFilter")
		}

		paramsUpdate.ReferrerID = &referrer.ID
	}

	if params.MarketingCode != nil && *params.MarketingCode != "" {
		filterMarketing := map[string]interface{}{
			"parent_id": profile.ParentID,
			"login_id":  strings.ToUpper(*params.MarketingCode),
			"status":    true,
		}

		marketing, err := s.repo.GetByFilter(ctx, filterMarketing)

		s.util.Logger.Info("UserService.Create.Marketing", zap.Any("Info", marketing))

		if err != nil {
			return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserService.s.repo.GetByFilter")
		}

		paramsUpdate.MarketingID = &marketing.ID
	}

	if params.VoucherCode != nil && *params.VoucherCode != "" {

		identifierRequest := request.Identifier{
			AccessToken: identifier.AuthToken,
		}

		newRequest := request.NewRequest(s.util, s.util.Conf.Get("GATEWAY_URL"))

		respVoucher, err := newRequest.ValidateVoucher(ctx, identifierRequest, *params.VoucherCode, internal.QueryObject{})
		if err != nil {
			return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserService.s.repo.GetByFilter")
		}

		if err := params.ValidateVoucher(respVoucher.Code); err != nil {
			return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, errors.New("invalid voucher"), riot.ErrorCodeInvalidArgument, "UserService.Create.newRequest.ValidateVoucher")
		}

		paramsUpdate.VoucherID = &respVoucher.Data.ID
	}

	filterUpdate := map[string]interface{}{
		"id":        id,
		"parent_id": profile.ParentID,
	}

	user, err := s.repo.UpdateByFilter(ctx, filterUpdate, paramsUpdate)
	if err != nil {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UserService.Update.s.repo.UpdateByFilter")
	}

	//update user security
	userSecurityParams := internal.CreateUserSecurity{
		LoginID:   &user.LoginID,
		Alias:     &user.LoginID,
		CreatedIP: params.CreatedIP,
	}

	filterUpdateSecurity := map[string]interface{}{
		"user": user.ID,
	}

	security, err := s.userSecuritySvc.UpdateByFilter(ctx, filterUpdateSecurity, userSecurityParams, nexusData)
	if err != nil {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UserService.Update.userSecuritySvc.Update")
	}

	filterSession := map[string]interface{}{
		"user": user.ID,
	}

	userSessionParams := internal.CreateSession{
		LoginID:   &user.LoginID,
		CreatedIP: params.CreatedIP,
	}

	session, err := s.sessionSvc.UpdateByFilter(ctx, filterSession, userSessionParams, nexusData)
	if err != nil {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UserService.Update.sessionSvc.UpdateByFilter")
	}

	//check if marchant
	if user.Role == riot.RoleCustomer {
		//create merchant
		if params.CreateMerchant != nil && *params.CreateMerchant {
			//publish event

			payload := internal.PayloadCreateMerchant{
				Username:    user.Username,
				LoginID:     user.LoginID,
				ProfileID:   user.ID,
				CompanyID:   profile.ParentID,
				ReferrerID:  user.ReferrerID,
				MarketingID: user.MarketingID,
				VoucherID:   user.VoucherID,
			}

			if err := s.publisher.PublishCreateMerchant(ctx, payload); err != nil {
				return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UserService.publisher.PublishCreateMerchant")
			}
		}
	}

	return internal.DataProfile{
		Profile: user,
		Security: internal.UserSecurityProtected{
			ID:            security.ID,
			User:          security.User,
			LoginID:       security.LoginID,
			Alias:         security.Alias,
			ResetLoginID:  security.ResetLoginID,
			ResetPassword: security.ResetPassword,
			Pin:           security.Pin,
			ResetPin:      security.ResetPin,
			WhitelistIP:   security.WhitelistIP,
			CreatedIP:     security.CreatedIP,
			CreatedAt:     security.CreatedAt,
			UpdatedAt:     security.UpdatedAt,
		},
		Session: session,
	}, nil
}

func (s *UserService) UpdateProfileSocial(ctx context.Context, identifier internal.Identifier, id string, params internal.UpdateUserSocialMedia, nexusData riot.Nexus) (_ internal.DataProfile, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("UserService", "Update"))

	if !s.cb.Ready() {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	// webservice := riot.GetNexusWebservice(nexusData)
	profile := riot.GetNexusProfile(nexusData)

	if params.Username != nil && *params.Username != "" {
		params.LoginID = params.Username
	}

	if err := params.Validate(); err != nil {

		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserService.UpdateProfileSocial.params.Validate")
	}

	paramsUpdate := internal.CreateUser{
		FirstName:           params.FirstName,
		LastName:            params.LastName,
		LoginID:             params.LoginID,
		Email:               params.Email,
		NickName:            params.NickName,
		MobilePhone:         params.MobilePhone,
		MobilePhoneCountry:  params.MobilePhoneCountry,
		MobilePhoneCode:     params.MobilePhoneCode,
		MobilePhoneNumber:   params.MobilePhoneNumber,
		MobilePhoneVerified: params.MobilePhoneVerified,
		Gender:              params.Gender,
		ReferrerCode:        params.ReferrerCode,
		TermsAccepted:       params.TermsAccepted,
		Avatar:              params.Avatar,
	}

	//check if reff is valid
	if params.ReferrerCode != nil && *params.ReferrerCode != "" {
		filterReferrer := map[string]interface{}{
			"parent_id": profile.ParentID,
			"login_id":  strings.ToUpper(*params.ReferrerCode),
			"status":    true,
		}

		referrer, err := s.repo.GetByFilter(ctx, filterReferrer)

		s.util.Logger.Info("UserService.UpdateProfileSocial.Referrer", zap.Any("Info", referrer))

		if err != nil {
			return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserService.UpdateProfileSocial.GetByFilter")
		}

		paramsUpdate.ReferrerID = &referrer.ID
	}

	if params.MarketingCode != nil && *params.MarketingCode != "" {
		filterMarketing := map[string]interface{}{
			"parent_id": profile.ParentID,
			"login_id":  strings.ToUpper(*params.MarketingCode),
			"status":    true,
		}

		marketing, err := s.repo.GetByFilter(ctx, filterMarketing)

		s.util.Logger.Info("UserService.UpdateProfileSocial.Marketing", zap.Any("Info", marketing))

		if err != nil {
			return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UpdateProfileSocial.UpdateProfileSocial.repo.GetByFilter")
		}

		paramsUpdate.MarketingID = &marketing.ID
	}

	if params.VoucherCode != nil && *params.VoucherCode != "" {

		identifierRequest := request.Identifier{
			AccessToken: identifier.AuthToken,
		}

		newRequest := request.NewRequest(s.util, s.util.Conf.Get("GATEWAY_URL"))

		respVoucher, err := newRequest.ValidateVoucher(ctx, identifierRequest, *params.VoucherCode, internal.QueryObject{})
		if err != nil {
			return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UpdateProfileSocial.UpdateProfileSocial.newRequest.ValidateVoucher")
		}

		if err := paramsUpdate.ValidateVoucher(respVoucher.Code); err != nil {
			return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, errors.New("invalid voucher"), riot.ErrorCodeInvalidArgument, "UpdateProfileSocial.UpdateProfileSocial.paramsUpdate.ValidateVoucher")
		}

		paramsUpdate.VoucherID = &respVoucher.Data.ID
	}

	filterUpdate := map[string]interface{}{
		"id":        id,
		"parent_id": profile.ParentID,
	}

	s.util.Logger.Info("UserService.UpdateProfileSocial.filterUpdate", zap.Any("Info", filterUpdate))
	s.util.Logger.Info("UserService.UpdateProfileSocial.paramsUpdate", zap.Any("Info", paramsUpdate))

	user, err := s.repo.UpdateByFilter(ctx, filterUpdate, paramsUpdate)
	if err != nil {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UpdateProfileSocial.UpdateProfileSocial.repo.UpdateByFilter")
	}

	filterByUserID := map[string]interface{}{
		"user": user.ID,
	}

	userSocialProviders, err := s.userSocialProviderRepo.GetAll(ctx, filterByUserID)

	if err != nil {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "AuthProvider.s.socialProviderRepo.GetByFilter")
	}

	session, err := s.sessionSvc.GetByFilter(ctx, filterByUserID, nexusData)
	if err != nil {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UpdateProfileSocial.UpdateProfileSocial.sessionSvc.GetByFilter")
	}

	if err := session.ValidateNotExist(); err != nil {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UpdateProfileSocial.UpdateProfileSocial.session.ValidateNotExist")
	}

	if user.LoginID != session.LoginID {
		//update session login id
		session, err = s.sessionSvc.UpdateManyByFilter(ctx, filterByUserID, internal.CreateSession{
			LoginID: &user.LoginID,
		}, nexusData)

		if err != nil {
			return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UpdateProfileSocial.UpdateProfileSocial.sessionSvc.UpdateByFilter")
		}

	}

	//check if marchant
	if user.Role == riot.RoleCustomer {
		//create merchant
		if params.CreateMerchant != nil && *params.CreateMerchant {
			//publish event

			payload := internal.PayloadCreateMerchant{
				Username:    user.Username,
				LoginID:     user.LoginID,
				ProfileID:   user.ID,
				CompanyID:   profile.ParentID,
				ReferrerID:  user.ReferrerID,
				MarketingID: user.MarketingID,
				VoucherID:   user.VoucherID,
			}

			if err := s.publisher.PublishCreateMerchant(ctx, payload); err != nil {
				return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UpdateProfileSocial.UpdateProfileSocial.publisher.PublishCreateMerchant")
			}
		}
	}

	return internal.DataProfile{
		Profile:         user,
		SocialProviders: &userSocialProviders,
		Session:         session,
	}, nil
}

func (s *UserService) Login(ctx context.Context, params internal.Login, nexusData riot.Nexus) (_ internal.DataProfile, err error) {

	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("UserService", "Login"))

	var user internal.User
	now := time.Now()

	if err := params.Validate(); err != nil {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserService.params.Validate")
	}

	username := pkg.FilterUsername(&params.Username)

	webservice := riot.GetNexusWebservice(nexusData)
	// profile := riot.GetNexusProfile(nexusData)

	filter := map[string]interface{}{
		"parent_id": webservice.ID,
		"login_id":  username,
	}

	if webservice.Role >= riot.RoleCompany {

		user, err = s.repo.GetByFilter(ctx, filter)

		if err != nil {
			return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserService.Login.repo.GetByFilter")
		}

		if err := user.ValidateNotExist(); err != nil {
			filter := map[string]interface{}{
				"id":       webservice.ID,
				"login_id": username,
			}

			user, err = s.repo.GetByFilter(ctx, filter)

			if err != nil {
				return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserService.Login.repo.GetByFilter")
			}

			if err := user.ValidateNotExist(); err != nil {

				filter := map[string]interface{}{
					"role":     riot.RoleSubaccount,
					"login_id": username,
				}

				user, err = s.repo.GetByFilter(ctx, filter)

				if err != nil {
					return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserService.Login.repo.GetByFilter")
				}
			}
		}
	} else {

		filter := map[string]interface{}{
			"login_id": username,
		}

		user, err = s.repo.GetByFilter(ctx, filter)

		if err != nil {
			return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserService.Login.repo.GetByFilter")
		}

		if err := user.ValidateNotExist(); err != nil {
			return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserService.Login.user.ValidateNotExist")
		}
	}

	//check role

	if err := user.CheckRole(webservice.Role); err != nil {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserService.Login.user.CheckRole")
	}

	if err := user.NotActive(); err != nil {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserService.Login.user.NotActive")
	}

	filterUserSecurity := map[string]interface{}{
		"login_id": username,
		"user":     user.ID,
	}

	//get user security
	userSecurity, err := s.userSecuritySvc.GetByFilter(ctx, filterUserSecurity, nexusData)

	if err != nil {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserService.Login.repo.GetByFilter")
	}

	//check password, descript
	decryptPassword, err := pkg.DecryptPassword(userSecurity.Password, []byte(s.util.Conf.Get("APP_KEY")))

	if err != nil {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserService.Login.repo.GetByFilter")
	}

	if err := userSecurity.CheckPassword(params.Password, decryptPassword); err != nil {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserService.Login.CheckPassword")
	}

	sessionParams := internal.CreateSession{
		Parent:    user.ParentID,
		User:      user.ID,
		Username:  &user.Username,
		LoginID:   &user.LoginID,
		CreatedIP: &params.CreatedIP,
		Forever:   params.Forever,
	}

	//create session
	session, err := s.sessionSvc.Create(ctx, sessionParams, user, nexusData)

	if err != nil {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserService.Login.repo.GetByFilter")
	}

	updateLastLogin := internal.CreateUser{
		LastLoggedIn: &now,
	}

	//update last login
	_, err = s.repo.Update(ctx, user.ID, updateLastLogin)

	if err != nil {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserService.Login.repo.GetByFilter")
	}

	return internal.DataProfile{
		Profile: user,
		Security: internal.UserSecurityProtected{
			ID:            userSecurity.ID,
			User:          userSecurity.User,
			LoginID:       userSecurity.LoginID,
			Alias:         userSecurity.Alias,
			ResetLoginID:  userSecurity.ResetLoginID,
			ResetPassword: userSecurity.ResetPassword,
			Pin:           userSecurity.Pin,
			ResetPin:      userSecurity.ResetPin,
			WhitelistIP:   userSecurity.WhitelistIP,
			CreatedIP:     userSecurity.CreatedIP,
			CreatedAt:     userSecurity.CreatedAt,
			UpdatedAt:     userSecurity.UpdatedAt,
		},
		Session: session,
	}, nil
}

func (s *UserService) Delete(ctx context.Context, id string, nexusData riot.Nexus) (err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("UserService", "Delete"))

	if !s.cb.Ready() {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	res, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UserService.s.repo.GetByID")
	}

	if err := res.ValidateNotExist(); err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeNotFound, "UserService.Delete.ValidateNotExist")
	}

	return s.repo.SoftDelete(ctx, id)
}

func (s *UserService) GetByID(ctx context.Context, id string, includes []string, nexusData riot.Nexus) (_ internal.User, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("UserService", "GetByID"))

	res, err := s.repo.GetByID(ctx, id)

	s.util.Logger.Info("UserService.GetByID", zap.Any("Info", res))

	if err != nil {
		return internal.User{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UserService.s.repo.GetByID")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.User{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeNotFound, "UserService.GetByID.ValidateNotExist")
	}

	return res, nil
}

func (s *UserService) GetByLoginID(ctx context.Context, loginID string, includes []string, nexusData riot.Nexus) (_ internal.User, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("UserService", "GetByLoginID"))

	filterProfile := map[string]interface{}{
		"login_id": loginID,
		"status":   true,
	}

	res, err := s.repo.GetByFilter(ctx, filterProfile)

	s.util.Logger.Info("UserService.GetByFilter", zap.Any("Info", res))

	if err != nil {
		return internal.User{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UserService.s.repo.GetByID")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.User{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeNotFound, "UserService.GetByID.ValidateNotExist")
	}

	return res, nil
}

func (s *UserService) GetSessionByUserID(ctx context.Context, id string, includes []string, nexusData riot.Nexus) (_ internal.UserSession, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("UserService", "GetByID"))

	if !s.cb.Ready() {
		return internal.UserSession{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	res, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return internal.UserSession{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UserService.s.repo.GetByID")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.UserSession{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeNotFound, "UserService.GetByID.ValidateNotExist")
	}

	//find session
	filterSession := map[string]interface{}{
		"user": res.ID,
	}
	session, err := s.sessionRepo.GetByFilter(ctx, filterSession)

	if err != nil {
		return internal.UserSession{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeNotFound, "UserService.GetByID.ValidateNotExist")
	}
	return internal.UserSession{
		Session: session.Session,
	}, nil
}

func (s *UserService) Session(ctx context.Context, identifier internal.IdentifierSession, nexusData riot.Nexus) (_ internal.DataProfile, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("UserService", "Session"))

	if !s.cb.Ready() {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	if err := identifier.Validate(); err != nil {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserService.params.Validate")
	}

	resSession, err := s.sessionSvc.GetBySession(ctx, identifier.Session, nexusData)
	if err != nil {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UserService.s.repo.GetByID")
	}

	if err := resSession.ValidateNotExist(); err != nil {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeNotFound, "UserService.GetByID.ValidateNotExist")
	}

	//get user
	user, err := s.repo.GetByID(ctx, resSession.User)
	if err != nil {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UserService.s.repo.GetByID")
	}

	if err := user.ValidateNotExist(); err != nil {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeNotFound, "UserService.GetByID.ValidateNotExist")
	}

	return internal.DataProfile{
		Profile: user,
		Session: resSession,
	}, nil
}

func (s *UserService) Validate(ctx context.Context, validateType string, urlValues url.Values, nexusData riot.Nexus) (internal.ResponseValidate, error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("UserService", "Validate"))

	webservice := riot.GetNexusWebservice(nexusData)
	profile := riot.GetNexusProfile(nexusData)

	var parent internal.ParentProfile
	defaultVaildate := internal.ResponseValidate{
		Validate: false,
	}

	var role int
	validReferrer := false

	if profile.ID != "" {
		parent = internal.ParentProfile{
			ParentID: profile.ParentID,
			ID:       profile.ID,
			Username: profile.Username,
			LoginID:  profile.LoginID,
			Role:     profile.Role,
		}

	} else {
		parent = internal.ParentProfile{
			ParentID: webservice.ParentID,
			ID:       webservice.ID,
			Username: webservice.Username,
			LoginID:  webservice.LoginID,
			Role:     webservice.Role,
		}
	}

	switch parent.Role {
	case riot.RoleCompany:
		role = riot.RoleCustomer
	case riot.RoleCorporateAdm:
		role = riot.RoleCorporate
	default:
		role = parent.Role + 1
	}

	if parent.Role > 6 {
		return defaultVaildate, riot.WrapErrorfLog(s.util.Logger, errors.New("role not allowed"), riot.ErrorCodeInvalidArgument, "UserService.NotAllow")
	}

	switch validateType {
	case "username":
		username := urlValues.Get("username")
		referrerCode := urlValues.Get("referrer_code")
		params := internal.CreateUser{
			Username: &username,
		}

		if referrerCode != "" {
			nexusData.Server.Profile = (*riot.NexusProfile)(&parent)
			validateReffCode, err := s.referrerCodeSvc.ValidateCode(ctx, referrerCode, nexusData)

			if err != nil {
				riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserService.Validate.referrerCodeSvc.ValidateCode")
			}

			if err := validateReffCode.CheckValidate(); err != nil {
				riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserService.Validate.referrerCodeSvc.ValidateCode")
			}

			validReferrer = validateReffCode.Validate
		}

		if err := params.ValidateUsernameLength(role, parent.Username, validReferrer); err != nil {
			return defaultVaildate, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserService.Validate.params.ValidateUsernameLength")
		}

		user, err := s.CheckDuplicateUsernameByParentID(ctx, parent.ID, params.Username)

		if err != nil {
			return defaultVaildate, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UserService.Validate.CheckDuplicateUsernameByParentID")
		}

		if err := user.ValidateExist(); err != nil {
			return internal.ResponseValidate{
				Validate: false,
			}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserService.Validate.CheckDuplicatePhoneByParentID.ValidateExist")
		}

	case "phone":
		mobilePhone := urlValues.Get("mobile_phone")

		user, err := s.CheckDuplicatePhoneByParentID(ctx, parent.ID, &mobilePhone)

		if err != nil {
			return defaultVaildate, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UserService.Validate.CheckDuplicatePhoneByParentID")
		}

		if err := user.ValidatePhoneExist(); err != nil {
			return internal.ResponseValidate{
				Validate: false,
			}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserService.Validate.CheckDuplicatePhoneByParentID.ValidateExist")
		}
	}

	return internal.ResponseValidate{
		Validate: true,
	}, nil
}

func (s *UserService) CheckDuplicateUsernameByParentID(ctx context.Context, parentID string, username *string) (internal.User, error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("UserService", "CheckDuplicateUsernameByParentID"))

	username = pkg.FilterUsername(username)

	filterByUsername := map[string]interface{}{
		"parent_id": parentID,
		"username":  username,
	}

	profile, err := s.repo.GetByFilter(ctx, filterByUsername)

	if err != nil {
		return internal.User{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UserService.CheckDuplicateByParentID.repo.GetByFilter")
	}

	if err := profile.ValidateExist(); err != nil {
		return profile, nil
	}

	filterByLoginID := map[string]interface{}{
		"parent_id": parentID,
		"login_id":  username,
	}

	profile, err = s.repo.GetByFilter(ctx, filterByLoginID)

	if err != nil {
		return internal.User{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UserService.CheckDuplicateByParentID.repo.GetByFilter")
	}

	if err := profile.ValidateExist(); err != nil {
		return profile, nil
	}

	return internal.User{}, nil
}

func (s *UserService) CheckDuplicatePhoneByParentID(ctx context.Context, parentID string, mobilePhone *string) (internal.User, error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("UserService", "CheckDuplicatePhoneByParentID"))

	mobilePhoneCode := s.util.Conf.Get("MOBILE_PHONE_CODE")
	mobilePhone = pkg.FilterMobilePhone(mobilePhone, &mobilePhoneCode)

	filterByUsername := map[string]interface{}{
		"parent_id":    parentID,
		"mobile_phone": mobilePhone,
	}

	profile, err := s.repo.GetByFilter(ctx, filterByUsername)

	if err != nil {
		return internal.User{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UserService.CheckDuplicateByParentID.repo.GetByFilter")
	}

	if err := profile.ValidateNotExist(); err != nil {
		return internal.User{}, nil
	}

	return profile, nil
}

func (s *UserService) CheckDuplicateEmailByParentID(ctx context.Context, parentID string, email *string) (internal.User, error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("UserService", "CheckDuplicateEmailByParentID"))

	filterByUsername := map[string]interface{}{
		"parent_id": parentID,
		"email":     strings.ToUpper(*email),
	}

	profile, err := s.repo.GetByFilter(ctx, filterByUsername)

	if err != nil {
		return internal.User{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UserService.CheckDuplicateByParentID.repo.GetByFilter")
	}

	if err := profile.ValidateNotExist(); err != nil {
		return internal.User{}, nil
	}

	return profile, nil
}

func (s *UserService) UpdateCredential(ctx context.Context, typeCredential string, params internal.UpdateUserSecurity, nexusData riot.Nexus) (_ internal.DataProfile, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("UserService", "UpdatePassword"))

	if !s.cb.Ready() {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	realProfile := riot.GetNexusRealProfile(nexusData)
	profile := riot.GetNexusProfile(nexusData)

	var profileID string

	parentReset := false

	if params.Downline != nil && *params.Downline != "" {
		profileID = *params.Downline
	} else {
		profileID = profile.ID
	}

	filterProfile := map[string]interface{}{
		"id": profileID,
	}

	resProfile, err := s.repo.GetByFilter(ctx, filterProfile)

	if err != nil {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UserService.UpdatePassword.repo.GetByFilter")
	}

	if err := resProfile.ValidateNotExist(); err != nil {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeNotFound, "UserService.UpdatePassword.resProfile.ValidateNotExist()")
	}

	// check is valid parent
	if !resProfile.IsValidParent(profile, true) {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, errors.New("profile not found"), riot.ErrorCodeInvalidArgument, "UserService.UpdatePassword.resProfile.IsValidParent()")
	}

	if realProfile.ID != profile.ID {
		parentReset = true
	}

	filterUserSecurity := map[string]interface{}{
		"login_id": resProfile.LoginID,
		"user":     resProfile.ID,
	}

	//get user security
	userSecurity, err := s.userSecuritySvc.GetByFilter(ctx, filterUserSecurity, nexusData)

	if err != nil {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserService.UpdatePassword.repo.GetByFilter")
	}

	if err := userSecurity.ValidateNotExist(); err != nil {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeNotFound, "UserService.UpdatePassword.userSecurity.ValidateNotExist")
	}

	//check old password
	if params.OldPassword != nil && *params.OldPassword != "" {
		//check password, descript
		decryptPassword, err := pkg.DecryptPassword(userSecurity.Password, []byte(s.util.Conf.Get("APP_KEY")))

		if err != nil {
			return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserService.UpdatePassword.repo.GetByFilter")
		}

		if err := userSecurity.CheckPassword(*params.OldPassword, decryptPassword); err != nil {
			return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserService.UpdatePassword.CheckPassword")
		}
	}

	//check old pin
	if params.OldPin != nil && *params.OldPin != "" {
		if err := userSecurity.CheckPin(*params.OldPin); err != nil {
			return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserService.UpdatePassword.CheckPin")
		}
	}

	//update user security
	updateUserSecurity := internal.CreateUserSecurity{}

	switch typeCredential {
	case "password":
		updateUserSecurity.User = userSecurity.User
		updateUserSecurity.Password = params.Password
		updateUserSecurity.ResetLoginID = &parentReset
		updateUserSecurity.CreatedIP = params.CreatedIP
	case "pin":
		updateUserSecurity.User = userSecurity.User
		updateUserSecurity.Pin = params.Pin
		updateUserSecurity.ResetLoginID = &parentReset
		updateUserSecurity.CreatedIP = params.CreatedIP
	}

	params.User = userSecurity.User

	if err := params.Validate(typeCredential); err != nil {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserService.UpdatePassword.params.Validate")
	}

	resUpdateSecurity, err := s.userSecuritySvc.Update(ctx, userSecurity.ID, updateUserSecurity, nexusData)

	if err != nil {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UserService.UpdatePassword.userSecuritySvc.Update")
	}

	filterSession := map[string]interface{}{
		"login_id": resProfile.LoginID,
		"user":     resProfile.ID,
	}

	//get session
	session, err := s.sessionSvc.GetByFilter(ctx, filterSession, nexusData)

	if err != nil {
		return internal.DataProfile{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UserService.UpdatePassword.sessionSvc.GetByFilter")
	}

	return internal.DataProfile{
		Profile: resProfile,
		Security: internal.UserSecurityProtected{
			ID:            resUpdateSecurity.ID,
			User:          resUpdateSecurity.User,
			LoginID:       resUpdateSecurity.LoginID,
			Alias:         resUpdateSecurity.Alias,
			ResetLoginID:  resUpdateSecurity.ResetLoginID,
			ResetPassword: resUpdateSecurity.ResetPassword,
			Pin:           resUpdateSecurity.Pin,
			ResetPin:      resUpdateSecurity.ResetPin,
			WhitelistIP:   resUpdateSecurity.WhitelistIP,
			CreatedIP:     resUpdateSecurity.CreatedIP,
			CreatedAt:     resUpdateSecurity.CreatedAt,
			UpdatedAt:     resUpdateSecurity.UpdatedAt,
		},
		Session: session,
	}, nil
}

func (s *UserService) GetAllWithPagination(ctx context.Context, urlValues url.Values, nexusData riot.Nexus) (_ internal.UserPagin, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("UserService", "GetAllWithPagination"))

	if !s.cb.Ready() {
		return internal.UserPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	var parent internal.ParentProfile

	webservice := riot.GetNexusWebservice(nexusData)
	profile := riot.GetNexusProfile(nexusData)

	var role int

	if profile.ID != "" {
		parent = internal.ParentProfile{
			ParentID: profile.ParentID,
			ID:       profile.ID,
			Username: profile.Username,
			LoginID:  profile.LoginID,
			Role:     profile.Role,
		}

	} else {
		parent = internal.ParentProfile{
			ParentID: webservice.ParentID,
			ID:       webservice.ID,
			Username: webservice.Username,
			LoginID:  webservice.LoginID,
			Role:     webservice.Role,
		}
	}

	switch parent.Role {
	case riot.RoleCompany:
		role = riot.RoleCustomer
	case riot.RoleCorporateAdm:
		role = riot.RoleCorporate
	default:
		role = parent.Role + 1
	}

	s.util.Logger.Info("role", zap.Any("info", role))

	// pagination
	paginationQry, _ := riot.QueryByPrefix(&urlValues, "pagination")
	sortQry, _ := riot.QueryByPrefix(&urlValues, "sort")
	dateRangeQry, _ := riot.QueryByPrefix(&urlValues, "date_range")
	statusQry, _ := url.QueryUnescape(urlValues.Get("status"))

	builder := mongorm.NewBuilder()

	// default value, if not set for pagination
	builder.BuildPagination(s.util.Logger, paginationQry)

	// default value, if not set for sort
	builder.BuildSort(s.util.Logger, sortQry)

	// default value, if not set range date
	builder.BuildRangeDate(s.util.Logger, dateRangeQry)

	// default value, if not set status
	builder.BuildStatus(s.util.Logger, statusQry)

	res, err := s.repo.GetAllWithPagination(ctx, *builder)
	if err != nil {
		return internal.UserPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UserService.repo.GetAllWithPagination")
	}

	return res, nil
}
