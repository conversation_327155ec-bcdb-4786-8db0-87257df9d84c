package services

import (
	"context"
	"net/url"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"

	"github.com/continue-team/hecarim/internal"
	"github.com/continue-team/hecarim/pkg"
	"github.com/continue-team/riot/pkg/logger"
	"github.com/mercari/go-circuitbreaker"
	"go.uber.org/zap"
)

type UserSecurityRepository interface {
	Create(ctx context.Context, params internal.CreateUserSecurity) (internal.UserSecurity, error)
	SoftDelete(ctx context.Context, id string) error
	Update(ctx context.Context, id string, params internal.CreateUserSecurity) (internal.UserSecurity, error)
	GetByID(ctx context.Context, id string) (internal.UserSecurity, error)
	GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.UserSecurity, error)
	GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.UserSecurityPagin, error)
	GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.UserSecurity, error)
	UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateUserSecurity) (internal.UserSecurity, error)
}

type UserSecurityService struct {
	repo UserSecurityRepository
	cb   *circuitbreaker.CircuitBreaker
	util riot.Util
}

func NewUserSecurityService(util riot.Util, repo UserSecurityRepository) *UserSecurityService {
	return &UserSecurityService{
		repo: repo,
		util: util,
		cb: circuitbreaker.New(
			circuitbreaker.WithOpenTimeout(time.Second*30),
			circuitbreaker.WithTripFunc(circuitbreaker.NewTripFuncConsecutiveFailures(3)),
			circuitbreaker.WithOnStateChangeHookFn(func(oldState, newState circuitbreaker.State) {
				util.Logger.Info("state changed",
					zap.String("oldState", string(oldState)),
					zap.String("newState", string(newState)),
				)
			}),
		),
	}
}

func (s *UserSecurityService) Create(ctx context.Context, params internal.CreateUserSecurity, nexusData riot.Nexus) (_ internal.UserSecurity, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("UserSecurityService", "Create"))

	if !s.cb.Ready() {
		return internal.UserSecurity{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	// webservice := riot.GetNexusWebservice(nexusData)
	// profile := riot.GetNexusProfile(nexusData)

	//enccrypt password
	encryptPassword, err := pkg.EncryptPassword(*params.Password, []byte(s.util.Conf.Get("APP_KEY")))
	if err != nil {
		return internal.UserSecurity{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UserSecurityService.pkg.EncryptPassword")
	}

	params.Password = &encryptPassword

	if err := params.Validate(); err != nil {
		return internal.UserSecurity{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "UserSecurityService.params.Validate")
	}

	res, err := s.repo.Create(ctx, params)
	if err != nil {
		return internal.UserSecurity{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UserSecurityService.s.repo.Create")
	}

	return res, nil
}

func (s *UserSecurityService) Delete(ctx context.Context, id string, nexusData riot.Nexus) (err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("UserSecurityService", "Delete"))

	if !s.cb.Ready() {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	res, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UserSecurityService.s.repo.GetByID")
	}

	if err := res.ValidateNotExist(); err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeNotFound, "UserSecurityService.Delete.ValidateNotExist")
	}

	return s.repo.SoftDelete(ctx, id)
}

func (s *UserSecurityService) Update(ctx context.Context, id string, params internal.CreateUserSecurity, nexusData riot.Nexus) (_ internal.UserSecurity, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("UserSecurityService", "Update"))

	if !s.cb.Ready() {
		return internal.UserSecurity{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	if params.Password != nil && *params.Password != "" {
		encryptPassword, err := pkg.EncryptPassword(*params.Password, []byte(s.util.Conf.Get("APP_KEY")))
		if err != nil {
			return internal.UserSecurity{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UserSecurityService.pkg.EncryptPassword")
		}

		params.Password = &encryptPassword
	}

	res, err := s.repo.Update(ctx, id, params)
	if err != nil {
		return internal.UserSecurity{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UserSecurityService.s.repo.Update")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.UserSecurity{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeNotFound, "UserSecurityService.Update.ValidateNotExist")
	}

	return res, nil
}

func (s *UserSecurityService) UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateUserSecurity, nexusData riot.Nexus) (_ internal.UserSecurity, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("UserSecurityService", "UpdateByFilter"))

	if !s.cb.Ready() {
		return internal.UserSecurity{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	res, err := s.repo.UpdateByFilter(ctx, filter, params)
	if err != nil {
		return internal.UserSecurity{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UserSecurityService.s.repo.Update")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.UserSecurity{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeNotFound, "UserSecurityService.Update.ValidateNotExist")
	}

	return res, nil
}

func (s *UserSecurityService) GetByID(ctx context.Context, id string, includes []string, nexusData riot.Nexus) (_ internal.UserSecurity, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("UserSecurityService", "GetByID"))

	if !s.cb.Ready() {
		return internal.UserSecurity{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	res, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return internal.UserSecurity{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UserSecurityService.s.repo.GetByID")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.UserSecurity{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeNotFound, "UserSecurityService.GetByID.ValidateNotExist")
	}

	return res, nil
}

func (s *UserSecurityService) GetByFilter(ctx context.Context, filter map[string]interface{}, nexusData riot.Nexus) (_ internal.UserSecurity, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("UserSecurityService", "GetByID"))

	if !s.cb.Ready() {
		return internal.UserSecurity{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	res, err := s.repo.GetByFilter(ctx, filter)
	if err != nil {
		return internal.UserSecurity{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UserSecurityService.s.repo.GetByID")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.UserSecurity{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeNotFound, "UserSecurityService.GetByID.ValidateNotExist")
	}

	return res, nil
}

func (s *UserSecurityService) GetByIDProtected(ctx context.Context, id string, includes []string, nexusData riot.Nexus) (_ internal.UserSecurityProtected, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("UserSecurityService", "GetByID"))

	if !s.cb.Ready() {
		return internal.UserSecurityProtected{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	res, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return internal.UserSecurityProtected{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UserSecurityService.s.repo.GetByID")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.UserSecurityProtected{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeNotFound, "UserSecurityService.GetByID.ValidateNotExist")
	}

	return internal.UserSecurityProtected{
		ID:            res.ID,
		User:          res.User,
		LoginID:       res.LoginID,
		Alias:         res.Alias,
		ResetLoginID:  res.ResetLoginID,
		ResetPassword: res.ResetPassword,
		Pin:           res.Pin,
		ResetPin:      res.ResetPin,
		WhitelistIP:   res.WhitelistIP,
		CreatedIP:     res.CreatedIP,
		CreatedAt:     res.CreatedAt,
		UpdatedAt:     res.UpdatedAt,
	}, nil
}

func (s *UserSecurityService) GetAllWithPagination(ctx context.Context, urlValues url.Values, nexusData riot.Nexus) (_ internal.UserSecurityPagin, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("UserSecurityService", "GetAllWithPagination"))

	if !s.cb.Ready() {
		return internal.UserSecurityPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	// pagination
	paginationQry, _ := riot.QueryByPrefix(&urlValues, "pagination")
	sortQry, _ := riot.QueryByPrefix(&urlValues, "sort")
	dateRangeQry, _ := riot.QueryByPrefix(&urlValues, "date_range")
	statusQry, _ := url.QueryUnescape(urlValues.Get("status"))

	builder := mongorm.NewBuilder()

	// default value, if not set for pagination
	builder.BuildPagination(s.util.Logger, paginationQry)

	// default value, if not set for sort
	builder.BuildSort(s.util.Logger, sortQry)

	// default value, if not set range date
	builder.BuildRangeDate(s.util.Logger, dateRangeQry)

	// default value, if not set status
	builder.BuildStatus(s.util.Logger, statusQry)

	res, err := s.repo.GetAllWithPagination(ctx, *builder)
	if err != nil {
		return internal.UserSecurityPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "UserSecurityService.repo.GetAllWithPagination")
	}

	return res, nil
}
