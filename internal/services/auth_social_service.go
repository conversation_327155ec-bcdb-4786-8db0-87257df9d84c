package services

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"strings"
	"time"

	"github.com/continue-team/riot"

	"github.com/continue-team/hecarim/internal"
	"github.com/continue-team/hecarim/internal/constant"
	"github.com/continue-team/hecarim/internal/firebaseauth"
	"github.com/continue-team/hecarim/internal/repositories"
	"github.com/continue-team/hecarim/pkg"
	"github.com/continue-team/riot/pkg/logger"
	"github.com/continue-team/riot/socialite"
	"github.com/continue-team/riot/socialite/providers/google"
	"github.com/continue-team/riot/socialite/providers/instagram"
	"github.com/continue-team/riot/socialite/providers/tiktok"
	"github.com/continue-team/riot/socialite/providers/twitterv2"
	"github.com/mercari/go-circuitbreaker"
	"go.uber.org/zap"
)

type AuthSocialService struct {
	cb                     *circuitbreaker.CircuitBreaker
	util                   riot.Util
	userRepo               *repositories.UserRepository
	userSocialProviderRepo *repositories.UserSocialProviderRepository
	socialProviderRepo     *repositories.SocialProviderRepository
	userSvc                *UserService
	sessionSvc             *SessionService
	redisCache             *internal.Redis
}

func NewAuthSocialService(util riot.Util, userRepo *repositories.UserRepository, userSocialProviderRepo *repositories.UserSocialProviderRepository, socialProviderRepo *repositories.SocialProviderRepository, userSvc *UserService, sessionSvc *SessionService, redisCache *internal.Redis) *AuthSocialService {
	return &AuthSocialService{
		util:                   util,
		userRepo:               userRepo,
		userSocialProviderRepo: userSocialProviderRepo,
		socialProviderRepo:     socialProviderRepo,
		userSvc:                userSvc,
		sessionSvc:             sessionSvc,
		redisCache:             redisCache,
		cb: circuitbreaker.New(
			circuitbreaker.WithOpenTimeout(time.Second*30),
			circuitbreaker.WithTripFunc(circuitbreaker.NewTripFuncConsecutiveFailures(3)),
			circuitbreaker.WithOnStateChangeHookFn(func(oldState, newState circuitbreaker.State) {
				util.Logger.Info("state changed",
					zap.String("oldState", string(oldState)),
					zap.String("newState", string(newState)),
				)
			}),
		),
	}
}

func (s *AuthSocialService) AuthProvider(ctx context.Context, identifier internal.AuthIdentifier, state string, nexusData riot.Nexus) (internal.DataProfileAuth, error) {

	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("AuthSocialService", "AuthProvider"))

	if err := identifier.Validate(); err != nil {
		return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "AuthProvider.identifier.Validate()")
	}

	//register providers
	err := s.RegisterProvider(ctx, state, identifier.Domain, nexusData)

	if err != nil {
		return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "AuthProvider.s.RegisterProvider")
	}

	// not auth code or token or verifier then get auth url
	if identifier.Code == "" && identifier.OauthToken == "" && identifier.OauthVerifier == "" {

		url, err := socialite.GetAuthURL(identifier.Res, identifier.Req)

		if err != nil {
			return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "AuthProvider.AuthProvider.socialite.GetAuthURL")
		}

		s.util.Logger.Info("AuthSocialService.AuthProvider.GetAuthURL", zap.String("Url", url))

		return internal.DataProfileAuth{
			Profile:         nil,
			Session:         nil,
			SocialProviders: nil,
			Url:             &url,
		}, nil
	}

	s.util.Logger.Info("AuthSocialService.AuthProvider", zap.String("Code", identifier.Code), zap.String("OauthToken", identifier.OauthToken), zap.String("OauthVerifier", identifier.OauthVerifier))

	user, err := socialite.CompleteUserAuth(identifier.Res, identifier.Req)

	if err != nil {
		return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "AuthSocialService.AuthProvider.socialite.CompleteUserAuth")
	}

	switch state {
	case "auth":
		s.util.Logger.Info("AuthSocialService.AuthProvider.User.Auth", zap.Any("Info", user))
		res, err := s.AuthProviderComplete(ctx, identifier, user, nexusData)

		if err != nil {
			return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "AuthSocialService.AuthProvider.s.AuthProviderComplete")
		}
		return res, nil

	case "connect":
		s.util.Logger.Info("AuthSocialService.AuthProvider.User.Connect", zap.Any("Info", user))
		res, err := s.AuthProviderConnect(ctx, identifier, user, nexusData)

		if err != nil {
			return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "AuthSocialService.AuthProvider.s.AuthProviderConnect")
		}
		return res, nil
	}

	return internal.DataProfileAuth{}, nil
}

func (s *AuthSocialService) AuthFBProvider(ctx context.Context, identifier internal.AuthIdentifier, userProvider *firebaseauth.FirebaseJWT, state string, nexusData riot.Nexus) (internal.DataProfileAuth, error) {

	s.util.Logger.Info("AuthSocialService.AuthFBProvider", zap.Any("Info", identifier))

	return s.AuthProviderFirebaseComplete(ctx, identifier, userProvider, nexusData)
}

func (s *AuthSocialService) AuthProviderComplete(ctx context.Context, identifier internal.AuthIdentifier, userProvider socialite.User, nexusData riot.Nexus) (internal.DataProfileAuth, error) {
	var parent internal.ParentProfile

	webservice := riot.GetNexusWebservice(nexusData)
	profile := riot.GetNexusProfile(nexusData)

	var role int

	if profile.ID != "" {
		parent = internal.ParentProfile{
			ParentID: profile.ParentID,
			ID:       profile.ID,
			Username: profile.Username,
			LoginID:  profile.LoginID,
			Role:     profile.Role,
		}

	} else {
		parent = internal.ParentProfile{
			ParentID: webservice.ParentID,
			ID:       webservice.ID,
			Username: webservice.Username,
			LoginID:  webservice.LoginID,
			Role:     webservice.Role,
		}
	}

	switch parent.Role {
	case riot.RoleCompany:
		role = riot.RoleCustomer

		if identifier.IsGroup == "true" {
			role = riot.RoleGroup
		}

	case riot.RoleCorporateAdm:
		role = riot.RoleCorporate
	default:
		role = parent.Role + 1
	}

	if err := parent.NotAllowByRole(); err != nil {
		return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "AuthSocialService.AuthProviderComplete.parent.NotAllowByRole")
	}

	//check if user already registered
	filterUserSocialProvider := map[string]interface{}{
		"user_id": userProvider.UserID,
		"parent":  parent.ID,
	}

	userSocialProvide, err := s.userSocialProviderRepo.GetByFilter(ctx, filterUserSocialProvider)

	if err != nil {
		return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "AuthSocialService.AuthProviderComplete.userSocialProviderRepo.GetByFilter")
	}

	// if already registered, return session
	if userSocialProvide.IsExist() {
		//filter user
		filterUser := map[string]interface{}{
			"id": userSocialProvide.User,
		}

		user, err := s.userRepo.GetByFilter(ctx, filterUser)

		if err != nil {
			return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "AuthSocialService.AuthProviderComplete.userSocialProvide.ValidateExist")
		}

		//filter user social provider
		filterByUserID := map[string]interface{}{
			"user": userSocialProvide.User,
		}

		userSocialProviders, err := s.userSocialProviderRepo.GetAll(ctx, filterByUserID)

		if err != nil {
			return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "AuthSocialService.AuthProviderComplete.userSocialProviderRepo.GetAll")
		}

		ip := pkg.GetIP(identifier.Req)

		sessionParams := internal.CreateSession{
			Parent:    user.ParentID,
			User:      user.ID,
			Username:  &user.Username,
			LoginID:   &user.LoginID,
			CreatedIP: &ip,
		}

		session, err := s.sessionSvc.Create(ctx, sessionParams, user, nexusData)

		if err != nil {
			return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "AuthProvider.AuthProviderComplete.sessionSvc.Create")
		}

		return internal.DataProfileAuth{
			Profile:         &user,
			Session:         &session,
			SocialProviders: &userSocialProviders,
			Url:             nil,
		}, nil
	}

	username := userProvider.RemoveRegexMail()

	if username == "" {
		//if email empty using display name
		username = userProvider.NickName
	}

	if username == "" {
		//if display name empty using username
		username = userProvider.Name
	}

	randomNumber := rand.Intn(10000)
	username = fmt.Sprintf("%s%d", username, randomNumber)

	params := internal.CreateUser{
		Username: &username,
		LoginID:  &username,
	}

	//find parent profile
	filterParent := map[string]interface{}{
		"id": parent.ID,
	}

	actor, err := s.userRepo.GetByFilter(ctx, filterParent)

	if err != nil {
		return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "AuthProvider.s.repo.GetByFilter")
	}

	if err := actor.ValidateParentNotExist(); err != nil {
		return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "AuthProvider.Profile.NotExist")
	}

	if err := actor.NotActive(); err != nil {
		return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "AuthProvider.Profile.NotActive")
	}

	usernameIsExist, err := s.userSvc.CheckDuplicateUsernameByParentID(ctx, parent.ID, params.Username)

	if err != nil {
		return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "AuthProvider.s.CheckDuplicateUsernameByParentID")
	}

	if err := usernameIsExist.ValidateExist(); err != nil {
		return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "AuthProvider.CheckDuplicateUsernameByParentID")
	}

	now := time.Now()
	mobilePhoneCode := s.util.Conf.Get("MOBILE_PHONE_CODE")
	mobilePhoneCountry := s.util.Conf.Get("MOBILE_PHONE_COUNTRY")
	status := true
	isSocialMedia := true
	ip := pkg.GetIP(identifier.Req)

	params.FirstName = &userProvider.FirstName
	params.LastName = &userProvider.LastName
	params.NickName = &userProvider.NickName
	params.Email = &userProvider.Email
	params.Avatar = &userProvider.AvatarURL
	params.ParentID = parent.ID
	params.ParentRole = &parent.Role
	params.ParentUsername = &parent.Username
	params.Role = &role
	params.LoginID = params.Username
	params.ParentUsername = &parent.Username
	params.LastLoggedIn = &now
	params.LastLoggedInIP = params.CreatedIP
	params.MobilePhoneCode = &mobilePhoneCode
	params.MobilePhoneCountry = &mobilePhoneCountry
	params.Status = &status
	params.CreatedIP = &ip
	params.IsSocialMedia = &isSocialMedia

	//generate username
	if parent.Role == riot.RoleCompany && parent.Role != 9 {
		filter := map[string]interface{}{
			"parent_id": parent.ID,
		}

		//get total user
		totalUser, err := s.userRepo.CountByFilter(ctx, filter)

		if err != nil {
			return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "AuthProvider.s.repo.CountByFilter")
		}

		for {
			totalUser++
			totalUserStr := fmt.Sprintf("%d", totalUser)
			params.Username = &totalUserStr

			// Ensure the username is 11 characters long, padding with leading zeros if necessary
			for len(*params.Username) < 11 {
				*params.Username = "0" + *params.Username
			}

			// Prepend parent's username to the generated username
			username := actor.Username + *params.Username
			*params.Username = username

			// Check if the username already exists
			isExist, err := s.userSvc.CheckDuplicateUsernameByParentID(ctx, parent.ID, params.Username)
			if err != nil {
				return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "AuthProvider.CheckDuplicateUsernameByParentID")
			}

			// Validate if the username exists or not
			if err := isExist.ValidateNotExist(); err != nil {
				// If it doesn't exist, exit the loop
				break
			}
		}

	}

	if err := params.ValidateSocialMedia(); err != nil {
		return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "AuthProvider.params.Validate")
	}

	user, err := s.userRepo.Create(ctx, params)
	if err != nil {
		return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "AuthProvider.s.repo.Create")
	}

	// create user social provider
	whitelistIP := "*"
	userSocialProviderParams := internal.CreateUserSocialProvider{
		User:        user.ID,
		Parent:      user.ParentID,
		LoginID:     &user.LoginID,
		Alias:       &user.Alias,
		Provider:    &userProvider.Provider,
		Email:       &userProvider.Email,
		Name:        &userProvider.Name,
		FirstName:   &userProvider.FirstName,
		LastName:    &userProvider.LastName,
		NickName:    &userProvider.NickName,
		Description: &userProvider.Description,
		UserID:      &userProvider.UserID,
		AvatarURL:   &userProvider.AvatarURL,
		Location:    &userProvider.Location,
		WhitelistIP: &whitelistIP,
		CreatedIP:   &ip,
	}

	_, err = s.userSocialProviderRepo.Create(ctx, userSocialProviderParams)

	if err != nil {
		return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "AuthProvider.s.userSocialProviderRepo.Create")
	}

	sessionParams := internal.CreateSession{
		Parent:    user.ParentID,
		User:      user.ID,
		Username:  &user.Username,
		LoginID:   &user.LoginID,
		CreatedIP: &ip,
	}

	session, err := s.sessionSvc.Create(ctx, sessionParams, user, nexusData)

	if err != nil {
		return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "AuthProvider.s.sessionSvc.Create")
	}

	//filter user social provider
	filterByUserID := map[string]interface{}{
		"user": user.ID,
	}

	userSocialProviders, err := s.userSocialProviderRepo.GetAll(ctx, filterByUserID)

	if err != nil {
		return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "AuthProvider.s.socialProviderRepo.GetByFilter")
	}

	return internal.DataProfileAuth{
		Profile:         &user,
		Session:         &session,
		SocialProviders: &userSocialProviders,
		Url:             nil,
	}, nil
}

func (s *AuthSocialService) AuthProviderFirebaseComplete(ctx context.Context, identifier internal.AuthIdentifier, userProvider *firebaseauth.FirebaseJWT, nexusData riot.Nexus) (internal.DataProfileAuth, error) {
	var parent internal.ParentProfile

	webservice := riot.GetNexusWebservice(nexusData)
	profile := riot.GetNexusProfile(nexusData)

	var role int

	if profile.ID != "" {
		parent = internal.ParentProfile{
			ParentID: profile.ParentID,
			ID:       profile.ID,
			Username: profile.Username,
			LoginID:  profile.LoginID,
			Role:     profile.Role,
		}

	} else {
		parent = internal.ParentProfile{
			ParentID: webservice.ParentID,
			ID:       webservice.ID,
			Username: webservice.Username,
			LoginID:  webservice.LoginID,
			Role:     webservice.Role,
		}
	}

	switch parent.Role {
	case riot.RoleCompany:
		role = riot.RoleCustomer
	case riot.RoleCorporateAdm:
		role = riot.RoleCorporate
	default:
		role = parent.Role + 1
	}

	if err := parent.NotAllowByRole(); err != nil {
		return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "AuthSocialService.AuthProviderComplete.parent.NotAllowByRole")
	}

	//check if user already registered
	filterUserSocialProvider := map[string]interface{}{
		"user_id": userProvider.Firebase.Identities.Google[0],
		"parent":  parent.ID,
	}

	userSocialProvide, err := s.userSocialProviderRepo.GetByFilter(ctx, filterUserSocialProvider)

	if err != nil {
		return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "AuthSocialService.AuthProviderComplete.userSocialProviderRepo.GetByFilter")
	}

	// if already registered, return session
	if userSocialProvide.IsExist() {
		//filter user
		filterUser := map[string]interface{}{
			"id": userSocialProvide.User,
		}

		user, err := s.userRepo.GetByFilter(ctx, filterUser)

		if err != nil {
			return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "AuthSocialService.AuthProviderComplete.userSocialProvide.ValidateExist")
		}

		//filter user social provider
		filterByUserID := map[string]interface{}{
			"user": userSocialProvide.User,
		}

		userSocialProviders, err := s.userSocialProviderRepo.GetAll(ctx, filterByUserID)

		if err != nil {
			return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "AuthSocialService.AuthProviderComplete.userSocialProviderRepo.GetAll")
		}

		ip := pkg.GetIP(identifier.Req)

		sessionParams := internal.CreateSession{
			Parent:    user.ParentID,
			User:      user.ID,
			Username:  &user.Username,
			LoginID:   &user.LoginID,
			CreatedIP: &ip,
		}

		session, err := s.sessionSvc.Create(ctx, sessionParams, user, nexusData)

		if err != nil {
			return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "AuthProvider.AuthProviderComplete.sessionSvc.Create")
		}

		return internal.DataProfileAuth{
			Profile:         &user,
			Session:         &session,
			SocialProviders: &userSocialProviders,
			Url:             nil,
		}, nil
	}

	username := userProvider.RemoveRegexMail()

	if username == "" {
		//if email empty using display name
		username = userProvider.UserID
	}

	if username == "" {
		//if display name empty using username
		username = userProvider.Name
	}

	randomNumber := rand.Intn(10000)
	username = fmt.Sprintf("%s%d", username, randomNumber)

	params := internal.CreateUser{
		Username: &username,
		LoginID:  &username,
	}

	//find parent profile
	filterParent := map[string]interface{}{
		"id": parent.ID,
	}

	actor, err := s.userRepo.GetByFilter(ctx, filterParent)

	if err != nil {
		return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "AuthProvider.s.repo.GetByFilter")
	}

	if err := actor.ValidateParentNotExist(); err != nil {
		return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "AuthProvider.Profile.NotExist")
	}

	if err := actor.NotActive(); err != nil {
		return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "AuthProvider.Profile.NotActive")
	}

	usernameIsExist, err := s.userSvc.CheckDuplicateUsernameByParentID(ctx, parent.ID, params.Username)

	if err != nil {
		return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "AuthProvider.s.CheckDuplicateUsernameByParentID")
	}

	if err := usernameIsExist.ValidateExist(); err != nil {
		return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "AuthProvider.CheckDuplicateUsernameByParentID")
	}

	now := time.Now()
	mobilePhoneCode := s.util.Conf.Get("MOBILE_PHONE_CODE")
	mobilePhoneCountry := s.util.Conf.Get("MOBILE_PHONE_COUNTRY")
	status := true
	isSocialMedia := true
	ip := pkg.GetIP(identifier.Req)

	params.Email = &userProvider.Email
	params.Avatar = &userProvider.Picture
	params.ParentID = parent.ID
	params.ParentRole = &parent.Role
	params.ParentUsername = &parent.Username
	params.Role = &role
	params.LoginID = params.Username
	params.ParentUsername = &parent.Username
	params.LastLoggedIn = &now
	params.LastLoggedInIP = params.CreatedIP
	params.MobilePhoneCode = &mobilePhoneCode
	params.MobilePhoneCountry = &mobilePhoneCountry
	params.Status = &status
	params.CreatedIP = &ip
	params.IsSocialMedia = &isSocialMedia

	//generate username
	if parent.Role == riot.RoleCompany && parent.Role != 9 {
		filter := map[string]interface{}{
			"parent_id": parent.ID,
		}

		//get total user
		totalUser, err := s.userRepo.CountByFilter(ctx, filter)

		if err != nil {
			return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "AuthProvider.s.repo.CountByFilter")
		}

		for {
			totalUser++
			totalUserStr := fmt.Sprintf("%d", totalUser)
			params.Username = &totalUserStr

			// Ensure the username is 11 characters long, padding with leading zeros if necessary
			for len(*params.Username) < 11 {
				*params.Username = "0" + *params.Username
			}

			// Prepend parent's username to the generated username
			username := actor.Username + *params.Username
			*params.Username = username

			// Check if the username already exists
			isExist, err := s.userSvc.CheckDuplicateUsernameByParentID(ctx, parent.ID, params.Username)
			if err != nil {
				return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "AuthProvider.CheckDuplicateUsernameByParentID")
			}

			// Validate if the username exists or not
			if err := isExist.ValidateNotExist(); err != nil {
				// If it doesn't exist, exit the loop
				break
			}
		}

	}

	if err := params.ValidateSocialMedia(); err != nil {
		return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "AuthProvider.params.Validate")
	}

	user, err := s.userRepo.Create(ctx, params)
	if err != nil {
		return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "AuthProvider.s.repo.Create")
	}

	// create user social provider
	whitelistIP := "*"
	provider := userProvider.RemoveRegexProvider()
	userSocialProviderParams := internal.CreateUserSocialProvider{
		User:        user.ID,
		Parent:      user.ParentID,
		LoginID:     &user.LoginID,
		Alias:       &user.Alias,
		Provider:    &provider,
		Email:       &userProvider.Email,
		Name:        &userProvider.Name,
		UserID:      &userProvider.Firebase.Identities.Google[0],
		AvatarURL:   &userProvider.Picture,
		WhitelistIP: &whitelistIP,
		CreatedIP:   &ip,
	}

	_, err = s.userSocialProviderRepo.Create(ctx, userSocialProviderParams)

	if err != nil {
		return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "AuthProvider.s.userSocialProviderRepo.Create")
	}

	sessionParams := internal.CreateSession{
		Parent:    user.ParentID,
		User:      user.ID,
		Username:  &user.Username,
		LoginID:   &user.LoginID,
		CreatedIP: &ip,
	}

	session, err := s.sessionSvc.Create(ctx, sessionParams, user, nexusData)

	if err != nil {
		return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "AuthProvider.s.sessionSvc.Create")
	}

	//filter user social provider
	filterByUserID := map[string]interface{}{
		"user": user.ID,
	}

	userSocialProviders, err := s.userSocialProviderRepo.GetAll(ctx, filterByUserID)

	if err != nil {
		return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "AuthProvider.s.socialProviderRepo.GetByFilter")
	}

	return internal.DataProfileAuth{
		Profile:         &user,
		Session:         &session,
		SocialProviders: &userSocialProviders,
		Url:             nil,
	}, nil
}

func (s *AuthSocialService) AuthProviderConnect(ctx context.Context, identifier internal.AuthIdentifier, userProvider socialite.User, nexusData riot.Nexus) (internal.DataProfileAuth, error) {

	profile := riot.GetNexusProfile(nexusData)

	if profile.ID == "" {
		return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, errors.New("session not exist"), riot.ErrorCodeInvalidArgument, "AuthSocialService.AuthProviderConnect.Profile.NotExist")
	}

	var userSocialProviders []internal.UserSocialProvider

	filterUserSocialProvider := map[string]interface{}{
		"parent":  profile.ParentID,
		"user_id": userProvider.UserID,
	}

	userSocialProvider, err := s.userSocialProviderRepo.GetByFilter(ctx, filterUserSocialProvider)

	if err != nil {
		return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "AuthSocialService.AuthProviderConnect.userSocialProviderRepo.GetByFilter")
	}

	filterUser := map[string]interface{}{
		"id": profile.ID,
	}

	user, err := s.userRepo.GetByFilter(ctx, filterUser)

	if err != nil {
		return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "AuthSocialService.AuthProviderConnect.userSocialProviderRepo.userRepo.GetByFilter")
	}

	if err := user.ValidateNotExist(); err != nil {
		return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeNotFound, "AuthSocialService.AuthProviderConnect.userSocialProviderRepo.GetByFilter")
	}

	filterSession := map[string]interface{}{
		"user": profile.ID,
	}

	session, err := s.sessionSvc.GetByFilter(ctx, filterSession, nexusData)

	if err != nil {
		return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "AuthSocialService.AuthProviderConnect.userSocialProviderRepo.GetByFilter")
	}

	if userSocialProvider.IsExist() {

		if err := userSocialProvider.AlreadyConnected(profile.ID); err != nil {
			return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "AuthSocialService.AuthProviderConnect.userSocialProvider.AlreadyConnected")
		}

		filterUserSocialProvider := map[string]interface{}{
			"user": profile.ID,
		}

		userSocialProviders, err = s.userSocialProviderRepo.GetAll(ctx, filterUserSocialProvider)
		if err != nil {
			return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "AuthSocialService.AuthProviderConnect.userSocialProviderRepo.GetByFilter")
		}

	} else {
		// create user social provider
		ip := pkg.GetIP(identifier.Req)
		whitelistIP := "*"
		userSocialProviderParams := internal.CreateUserSocialProvider{
			User:        user.ID,
			Parent:      user.ParentID,
			LoginID:     &user.LoginID,
			Alias:       &user.Alias,
			Provider:    &userProvider.Provider,
			Email:       &userProvider.Email,
			Name:        &userProvider.Name,
			FirstName:   &userProvider.FirstName,
			LastName:    &userProvider.LastName,
			NickName:    &userProvider.NickName,
			Description: &userProvider.Description,
			UserID:      &userProvider.UserID,
			AvatarURL:   &userProvider.AvatarURL,
			Location:    &userProvider.Location,
			WhitelistIP: &whitelistIP,
			CreatedIP:   &ip,
		}

		_, err = s.userSocialProviderRepo.Create(ctx, userSocialProviderParams)

		if err != nil {
			return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "AuthSocialService.AuthProviderConnect.userSocialProviderRepo.GetByFilter")
		}

		filterUserSocialProvider := map[string]interface{}{
			"user": profile.ID,
		}

		userSocialProviders, err = s.userSocialProviderRepo.GetAll(ctx, filterUserSocialProvider)
		if err != nil {
			return internal.DataProfileAuth{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "AuthSocialService.AuthProviderConnect.userSocialProviderRepo.GetByFilter")
		}

	}

	return internal.DataProfileAuth{
		Profile:         &user,
		Session:         &session,
		SocialProviders: &userSocialProviders,
	}, nil
}

func (s *AuthSocialService) RegisterProvider(ctx context.Context, state, domain string, nexusData riot.Nexus) error {
	var viders []socialite.Provider

	webservice := riot.GetNexusWebservice(nexusData)

	//register social providers
	filterProvider := map[string]interface{}{
		"profile": webservice.ID,
	}

	resProviders, err := s.socialProviderRepo.GetAll(ctx, filterProvider)
	if err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "AuthProvider.s.RegisterProvider.GetByFilter")
	}

	if domain == "" {
		domain = s.util.Conf.Get("DEFAULT_DOMAIN_CALLBACK")
	}

	for _, provider := range resProviders {

		var callback string = ""
		if state == constant.StateAuth {
			callback = provider.AuthCallback

			callback = strings.ReplaceAll(callback, "{{domain}}", domain)
		} else {
			callback = provider.ConnectCallback
			callback = strings.ReplaceAll(callback, "{{domain}}", domain)
		}

		switch provider.Name {
		case "google":
			viders = append(viders, google.New(provider.AuthKey, provider.AuthSecret, callback, provider.AuthScopes))
		case "tiktok":
			viders = append(viders, tiktok.New(provider.AuthKey, provider.AuthSecret, callback, provider.AuthScopes))
		case "instagram":
			viders = append(viders, instagram.New(provider.AuthKey, provider.AuthSecret, callback, provider.AuthScopes))
		case "twitterv2":
			viders = append(viders, twitterv2.NewAuthenticate(provider.AuthKey, provider.AuthSecret, callback))
		}

	}

	//reset providers
	socialite.ClearProviders()

	//register
	socialite.UseProviders(viders...)

	return nil
}
