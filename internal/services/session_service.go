package services

import (
	"context"
	"net/url"
	"strconv"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"

	"github.com/continue-team/hecarim/internal"
	"github.com/continue-team/hecarim/pkg"
	"github.com/continue-team/riot/pkg/logger"
	"github.com/mercari/go-circuitbreaker"
	"go.uber.org/zap"
)

type SessionRepository interface {
	Create(ctx context.Context, params internal.CreateSession) (internal.Session, error)
	SoftDelete(ctx context.Context, id string) error
	Update(ctx context.Context, id string, params internal.CreateSession) (internal.Session, error)
	GetByID(ctx context.Context, id string) (internal.Session, error)
	GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.Session, error)
	GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.SessionPagin, error)
	GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.Session, error)
	SoftDeleteByFilter(ctx context.Context, filter map[string]interface{}) error
	UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateSession) (internal.Session, error)
	UpdateManyByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateSession) (internal.Session, error)
	GetByFilterWithOptions(ctx context.Context, filter map[string]interface{}) (internal.Session, error)
}

type SessionService struct {
	repo       SessionRepository
	redisCache *internal.Redis
	cb         *circuitbreaker.CircuitBreaker
	util       riot.Util
}

func NewSessionService(util riot.Util, repo SessionRepository, redisCache *internal.Redis) *SessionService {
	return &SessionService{
		repo:       repo,
		util:       util,
		redisCache: redisCache,
		cb: circuitbreaker.New(
			circuitbreaker.WithOpenTimeout(time.Second*30),
			circuitbreaker.WithTripFunc(circuitbreaker.NewTripFuncConsecutiveFailures(3)),
			circuitbreaker.WithOnStateChangeHookFn(func(oldState, newState circuitbreaker.State) {
				util.Logger.Info("state changed",
					zap.String("oldState", string(oldState)),
					zap.String("newState", string(newState)),
				)
			}),
		),
	}
}

func (s *SessionService) Create(ctx context.Context, params internal.CreateSession, user internal.User, nexusData riot.Nexus) (_ internal.Session, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("SessionService", "Create"))

	if !s.cb.Ready() {
		return internal.Session{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	var ttlRedis time.Duration

	if err := params.Validate(); err != nil {
		return internal.Session{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "SessionService.params.Validate")
	}

	expireStr := s.util.Conf.Get("SESSION_EXPIRE")
	expireInt, err := strconv.Atoi(expireStr)
	if err != nil {
		return internal.Session{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "SessionService.Service.Register.Atoi")
	}

	// validUntil := pkg.SetTemporaryExpiredTime()
	validUntil := pkg.SetExpireInDays(expireInt)

	// if forever
	if params.Forever {
		expireStr := s.util.Conf.Get("SESSION_FOREVER_EXPIRE")
		expireInt, err := strconv.Atoi(expireStr)
		if err != nil {
			return internal.Session{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "SessionService.Service.Atoi")
		}
		validUntil = pkg.SetExpireInYears(expireInt)
	}

	actionDate := pkg.GetCurrentDateTime()

	//generate token session
	paramsGenerateToken := internal.CreateTokenSession{
		Util: s.util,
		Profile: func() internal.Profile {
			if params.User != "" {
				return internal.Profile{
					ID:        user.ID,
					ParentID:  user.ParentID,
					Username:  user.Username,
					LoginID:   user.LoginID,
					Role:      user.Role,
					CreatedIP: user.CreatedIP,
				}
			}
			return internal.Profile{}
		}(),
		IsGuest:    false,
		IsBot:      false,
		ActionDate: actionDate,
		ValidUntil: validUntil,
	}

	session, err := paramsGenerateToken.GenerateToken()
	if err != nil {
		return internal.Session{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "SessionService.s.GenerateToken")
	}

	filterInactive := map[string]interface{}{
		"user": params.User,
	}

	err = s.repo.SoftDeleteByFilter(ctx, filterInactive)

	if err != nil {
		return internal.Session{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "SessionService.s.repo.SoftDeleteByFilter")
	}

	guest := false
	online := true

	params.Session = &session.Jwt
	params.Guest = &guest
	params.Online = &online
	params.ValidUntil = &validUntil

	res, err := s.repo.Create(ctx, params)
	if err != nil {
		return internal.Session{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "SessionService.s.repo.Create")
	}

	parsedTime, err := pkg.ParseDateString(validUntil, pkg.DateLayoutDefault)
	if err != nil {
		return internal.Session{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "SessionService.Create.ParseDateString")
	}

	ttlRedis = time.Until(parsedTime)

	err = s.redisCache.SetSessionProfileFromRedis(ctx, res.User, &res, ttlRedis)

	if err != nil {
		return internal.Session{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "SessionService.Register.SetSessionProfileFromRedis")
	}

	return res, nil
}

func (s *SessionService) Delete(ctx context.Context, id string, nexusData riot.Nexus) (err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("SessionService", "Delete"))

	if !s.cb.Ready() {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	res, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "SessionService.s.repo.GetByID")
	}

	if err := res.ValidateNotExist(); err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeNotFound, "SessionService.Delete.ValidateNotExist")
	}

	return s.repo.SoftDelete(ctx, id)
}

func (s *SessionService) Update(ctx context.Context, id string, params internal.CreateSession, nexusData riot.Nexus) (_ internal.Session, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("SessionService", "Update"))

	if !s.cb.Ready() {
		return internal.Session{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	res, err := s.repo.Update(ctx, id, params)
	if err != nil {
		return internal.Session{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "SessionService.s.repo.Update")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.Session{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeNotFound, "SessionService.Update.ValidateNotExist")
	}

	return res, nil
}

func (s *SessionService) UpdateByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateSession, nexusData riot.Nexus) (_ internal.Session, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("SessionService", "UpdateByFilter"))

	if !s.cb.Ready() {
		return internal.Session{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	s.util.Logger.Debug("SessionService.UpdateByFilter", zap.Any("filter", filter), zap.Any("params", params))

	res, err := s.repo.UpdateByFilter(ctx, filter, params)
	if err != nil {
		return internal.Session{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "SessionService.s.repo.Update")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.Session{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeNotFound, "SessionService.Update.ValidateNotExist")
	}

	return res, nil
}

func (s *SessionService) UpdateManyByFilter(ctx context.Context, filter map[string]interface{}, params internal.CreateSession, nexusData riot.Nexus) (_ internal.Session, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("SessionService", "UpdateByFilter"))

	if !s.cb.Ready() {
		return internal.Session{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	s.util.Logger.Debug("SessionService.UpdateManyByFilter", zap.Any("filter", filter), zap.Any("params", params))

	res, err := s.repo.UpdateManyByFilter(ctx, filter, params)
	if err != nil {
		return internal.Session{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "SessionService.s.repo.Update")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.Session{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeNotFound, "SessionService.Update.ValidateNotExist")
	}

	return res, nil
}

func (s *SessionService) GetByID(ctx context.Context, id string, includes []string, nexusData riot.Nexus) (_ internal.Session, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("SessionService", "GetByID"))

	if !s.cb.Ready() {
		return internal.Session{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	res, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return internal.Session{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "SessionService.s.repo.GetByID")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.Session{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeNotFound, "SessionService.GetByID.ValidateNotExist")
	}

	return res, nil
}

func (s *SessionService) GetBySession(ctx context.Context, session string, nexusData riot.Nexus) (internal.Session, error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("SessionService", "GetBySession"))

	var parent internal.ParentProfile

	webservice := riot.GetNexusWebservice(nexusData)
	profile := riot.GetNexusProfile(nexusData)

	filter := map[string]interface{}{
		"session": session,
	}

	if profile.ID != "" {
		parent = internal.ParentProfile{
			ParentID: profile.ParentID,
			ID:       profile.ID,
			Username: profile.Username,
			LoginID:  profile.LoginID,
			Role:     profile.Role,
		}

		filter["parent"] = parent.ParentID

	} else {
		parent = internal.ParentProfile{
			ParentID: webservice.ParentID,
			ID:       webservice.ID,
			Username: webservice.Username,
			LoginID:  webservice.LoginID,
			Role:     webservice.Role,
		}

		filter["parent"] = parent.ID
	}

	res, err := s.repo.GetByFilter(ctx, filter)
	if err != nil {
		return internal.Session{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "SessionService.s.repo.GetByFilter")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.Session{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeNotFound, "SessionService.GetByID.ValidateNotExist")
	}

	//check validate session
	if err := res.Validate(); err != nil {
		return internal.Session{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeNotFound, "SessionService.GetByID.ValidateNotExist")
	}

	return res, nil
}

func (s *SessionService) GetByFilter(ctx context.Context, filter map[string]interface{}, nexusData riot.Nexus) (internal.Session, error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("SessionService", "GetByID"))

	s.util.Logger.Debug("SessionService.GetByFilter", zap.Any("filter", filter))

	res, err := s.repo.GetByFilterWithOptions(ctx, filter)
	if err != nil {
		return internal.Session{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "SessionService.s.repo.GetByFilterWithOptions")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.Session{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeNotFound, "SessionService.GetByFilter.ValidateNotExist")
	}

	return res, nil
}

func (s *SessionService) GetAllWithPagination(ctx context.Context, urlValues url.Values, nexusData riot.Nexus) (_ internal.SessionPagin, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("SessionService", "GetAllWithPagination"))

	if !s.cb.Ready() {
		return internal.SessionPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	// pagination
	paginationQry, _ := riot.QueryByPrefix(&urlValues, "pagination")
	sortQry, _ := riot.QueryByPrefix(&urlValues, "sort")
	dateRangeQry, _ := riot.QueryByPrefix(&urlValues, "date_range")
	statusQry, _ := url.QueryUnescape(urlValues.Get("status"))

	builder := mongorm.NewBuilder()

	// default value, if not set for pagination
	builder.BuildPagination(s.util.Logger, paginationQry)

	// default value, if not set for sort
	builder.BuildSort(s.util.Logger, sortQry)

	// default value, if not set range date
	builder.BuildRangeDate(s.util.Logger, dateRangeQry)

	// default value, if not set status
	builder.BuildStatus(s.util.Logger, statusQry)

	res, err := s.repo.GetAllWithPagination(ctx, *builder)
	if err != nil {
		return internal.SessionPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "SessionService.repo.GetAllWithPagination")
	}

	return res, nil
}
