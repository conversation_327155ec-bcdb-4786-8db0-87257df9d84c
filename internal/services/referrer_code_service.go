package services

import (
	"context"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/continue-team/hecarim/internal"
	"github.com/continue-team/hecarim/pkg"
	"github.com/continue-team/riot/pkg/logger"
	"github.com/mercari/go-circuitbreaker"
	"go.uber.org/zap"
)

type ReferrerCodeRepository interface {
	Create(ctx context.Context, params internal.CreateReferrerCode) (internal.ReferrerCode, error)
	SoftDelete(ctx context.Context, id string) error
	Update(ctx context.Context, id string, params internal.CreateReferrerCode) (internal.ReferrerCode, error)
	GetByID(ctx context.Context, id string) (internal.ReferrerCode, error)
	GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.ReferrerCode, error)
	GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.ReferrerCodePagin, error)
	GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.ReferrerCode, error)
}

type ReferrerCodeService struct {
	repo ReferrerCodeRepository
	cb   *circuitbreaker.CircuitBreaker
	util riot.Util
}

func NewReferrerCodeService(util riot.Util, repo ReferrerCodeRepository) *ReferrerCodeService {
	return &ReferrerCodeService{
		repo: repo,
		util: util,
		cb: circuitbreaker.New(
			circuitbreaker.WithOpenTimeout(time.Second*30),
			circuitbreaker.WithTripFunc(circuitbreaker.NewTripFuncConsecutiveFailures(3)),
			circuitbreaker.WithOnStateChangeHookFn(func(oldState, newState circuitbreaker.State) {
				util.Logger.Info("state changed",
					zap.String("oldState", string(oldState)),
					zap.String("newState", string(newState)),
				)
			}),
		),
	}
}

func (s *ReferrerCodeService) Create(ctx context.Context, params internal.CreateReferrerCode, nexusData riot.Nexus) (_ internal.ReferrerCode, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("ReferrerCodeService", "Create"))

	if !s.cb.Ready() {
		return internal.ReferrerCode{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	expireStr := s.util.Conf.Get("APP_ID_EXPIRE")
	expireInt, err := strconv.Atoi(expireStr)
	if err != nil {
		return internal.ReferrerCode{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "ReferrerCode.Service.Atoi")
	}

	validUntil := pkg.SetExpireInYears(expireInt)

	params.ValidUntil = &validUntil
	params.Profile = profile.ID
	status := true
	params.Status = &status

	if err := params.Validate(); err != nil {
		return internal.ReferrerCode{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "ReferrerCodeService.params.Validate")
	}

	//check referral is exist
	filter := map[string]interface{}{
		"code":    strings.ToUpper(*params.Code),
		"profile": profile.ID,
	}

	referrerCode, err := s.repo.GetByFilter(ctx, filter)
	if err != nil {
		return internal.ReferrerCode{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "ReferrerCodeService.s.repo.GetByFilter")
	}

	if err := referrerCode.ValidateExist(); err != nil {
		return internal.ReferrerCode{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "ReferrerCodeService.referrerCode.ValidateExist")
	}

	res, err := s.repo.Create(ctx, params)
	if err != nil {
		return internal.ReferrerCode{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "ReferrerCodeService.s.repo.Create")
	}

	return res, nil
}

func (s *ReferrerCodeService) Update(ctx context.Context, id string, params internal.CreateReferrerCode, nexusData riot.Nexus) (_ internal.ReferrerCode, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("ReferrerCodeService", "Update"))

	if !s.cb.Ready() {
		return internal.ReferrerCode{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	res, err := s.repo.Update(ctx, id, params)
	if err != nil {
		return internal.ReferrerCode{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "ReferrerCodeService.s.repo.Update")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.ReferrerCode{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeNotFound, "ReferrerCodeService.Update.ValidateNotExist")
	}

	return res, nil
}

func (s *ReferrerCodeService) GetByID(ctx context.Context, id string, includes []string, nexusData riot.Nexus) (_ internal.ReferrerCode, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("ReferrerCodeService", "GetByID"))

	if !s.cb.Ready() {
		return internal.ReferrerCode{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filter := map[string]interface{}{
		"id":      id,
		"profile": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filter)
	if err != nil {
		return internal.ReferrerCode{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "ReferrerCodeService.s.repo.GetByID")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.ReferrerCode{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeNotFound, "ReferrerCodeService.GetByID.ValidateNotExist")
	}

	return res, nil
}

func (s *ReferrerCodeService) Delete(ctx context.Context, id string, nexusData riot.Nexus) (err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("ReferrerCodeService", "Delete"))

	if !s.cb.Ready() {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	filter := map[string]interface{}{
		"id":      id,
		"profile": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filter)
	if err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "ReferrerCodeService.s.repo.GetByID")
	}

	if err := res.ValidateNotExist(); err != nil {
		return riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeNotFound, "ReferrerCodeService.Delete.ValidateNotExist")
	}

	return s.repo.SoftDelete(ctx, id)
}

func (s *ReferrerCodeService) ValidateCode(ctx context.Context, referrerCode string, nexusData riot.Nexus) (_ internal.ReferrerCodeValidate, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("ReferrerCodeService", "GetByID"))

	if !s.cb.Ready() {
		return internal.ReferrerCodeValidate{
			Validate: false,
		}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	profile := riot.GetNexusProfile(nexusData)

	//check if code is valid
	params := internal.CreateReferrerCode{
		Code:    &referrerCode,
		Profile: profile.ID,
	}

	if err := params.Validate(); err != nil {
		return internal.ReferrerCodeValidate{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "ReferrerCodeService.ValidateCode.params.Validate")
	}

	filter := map[string]interface{}{
		"code":    strings.ToUpper(referrerCode),
		"profile": profile.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filter)
	if err != nil {
		return internal.ReferrerCodeValidate{
			Validate: false,
		}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "ReferrerCodeService.s.repo.GetByID")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.ReferrerCodeValidate{
			Validate: false,
		}, nil
	}

	return internal.ReferrerCodeValidate{
		Validate:   true,
		Profile:    res.Profile,
		Code:       res.Code,
		ValidUntil: res.ValidUntil,
	}, nil
}

func (s *ReferrerCodeService) GetAllWithPagination(ctx context.Context, urlValues url.Values, nexusData riot.Nexus) (_ internal.ReferrerCodePagin, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("ReferrerCodeService", "GetAllWithPagination"))

	profile := riot.GetNexusProfile(nexusData)

	if !s.cb.Ready() {
		return internal.ReferrerCodePagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	// pagination
	paginationQry, _ := riot.QueryByPrefix(&urlValues, "pagination")
	sortQry, _ := riot.QueryByPrefix(&urlValues, "sort")
	dateRangeQry, _ := riot.QueryByPrefix(&urlValues, "date_range")
	statusQry, _ := url.QueryUnescape(urlValues.Get("status"))

	builder := mongorm.NewBuilder()

	filterMap, ok := builder.Filter.(bson.M)
	if !ok {
		filterMap = make(map[string]interface{})
	}

	filterMap["profile"] = profile.ID

	// default value, if not set for pagination
	builder.BuildPagination(s.util.Logger, paginationQry)

	// default value, if not set for sort
	builder.BuildSort(s.util.Logger, sortQry)

	// default value, if not set range date
	builder.BuildRangeDate(s.util.Logger, dateRangeQry)

	// default value, if not set status
	builder.BuildStatus(s.util.Logger, statusQry)

	res, err := s.repo.GetAllWithPagination(ctx, *builder)
	if err != nil {
		return internal.ReferrerCodePagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "ReferrerCodeService.repo.GetAllWithPagination")
	}

	return res, nil
}
