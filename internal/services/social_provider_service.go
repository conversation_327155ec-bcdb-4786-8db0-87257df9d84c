package services

import (
	"context"
	"net/url"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/continue-team/hecarim/internal"
	"github.com/continue-team/riot/pkg/logger"
	"github.com/mercari/go-circuitbreaker"
	"go.uber.org/zap"
)

type SocialProviderRepository interface {
	Create(ctx context.Context, params internal.CreateSocialProvider) (internal.SocialProvider, error)
	SoftDelete(ctx context.Context, id string) error
	Update(ctx context.Context, id string, params internal.CreateSocialProvider) (internal.SocialProvider, error)
	GetByID(ctx context.Context, id string) (internal.SocialProvider, error)
	GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.SocialProvider, error)
	GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.SocialProviderPagin, error)
	GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.SocialProvider, error)
}

type SocialProviderService struct {
	repo       SocialProviderRepository
	cb         *circuitbreaker.CircuitBreaker
	util       riot.Util
	redisCache *internal.Redis
}

func NewSocialProviderService(util riot.Util, repo SocialProviderRepository, redisCache *internal.Redis) *SocialProviderService {
	return &SocialProviderService{
		repo:       repo,
		util:       util,
		redisCache: redisCache,
		cb: circuitbreaker.New(
			circuitbreaker.WithOpenTimeout(time.Second*30),
			circuitbreaker.WithTripFunc(circuitbreaker.NewTripFuncConsecutiveFailures(3)),
			circuitbreaker.WithOnStateChangeHookFn(func(oldState, newState circuitbreaker.State) {
				util.Logger.Info("state changed",
					zap.String("oldState", string(oldState)),
					zap.String("newState", string(newState)),
				)
			}),
		),
	}
}

func (s *SocialProviderService) Create(ctx context.Context, params internal.CreateSocialProvider, nexusData riot.Nexus) (_ internal.SocialProvider, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("SocialProviderService", "Create"))

	if !s.cb.Ready() {
		return internal.SocialProvider{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	webservice := riot.GetNexusWebservice(nexusData)

	statusActive := true
	version := 1
	params.Profile = webservice.ID
	params.Status = &statusActive
	params.Version = &version

	if err := params.Validate(); err != nil {
		return internal.SocialProvider{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "SocialProviderService.Create.params.Validate")
	}

	filter := map[string]interface{}{
		"name":    params.Name,
		"profile": webservice.ID,
	}

	//check provider exist
	res, err := s.repo.GetByFilter(ctx, filter)
	if err != nil {
		return internal.SocialProvider{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "SocialProviderService.Create.s.repo.GetByID")
	}

	if err := res.ValidateExist(); err != nil {
		return internal.SocialProvider{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "SocialProviderService.Create.ValidateExist")
	}

	res, err = s.repo.Create(ctx, params)
	if err != nil {
		return internal.SocialProvider{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "SocialProviderService.Create.s.repo.Create")
	}

	return res, nil
}

func (s *SocialProviderService) Update(ctx context.Context, id string, params internal.CreateSocialProvider, nexusData riot.Nexus) (_ internal.SocialProvider, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("SocialProviderService", "Update"))

	if !s.cb.Ready() {
		return internal.SocialProvider{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	webservice := riot.GetNexusWebservice(nexusData)

	filter := map[string]interface{}{
		"id":      id,
		"profile": webservice.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filter)
	if err != nil {
		return internal.SocialProvider{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "SocialProviderService.s.repo.GetByID")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.SocialProvider{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeNotFound, "SocialProviderService.GetByID.ValidateNotExist")
	}

	res, err = s.repo.Update(ctx, id, params)
	if err != nil {
		return internal.SocialProvider{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "SocialProviderService.s.repo.Update")
	}

	return res, nil
}

func (s *SocialProviderService) GetByID(ctx context.Context, id string, includes []string, nexusData riot.Nexus) (_ internal.SocialProvider, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("SocialProviderService", "GetByID"))

	if !s.cb.Ready() {
		return internal.SocialProvider{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	webservice := riot.GetNexusWebservice(nexusData)

	filter := map[string]interface{}{
		"id":      id,
		"profile": webservice.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filter)
	if err != nil {
		return internal.SocialProvider{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "SocialProviderService.s.repo.GetByID")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.SocialProvider{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeNotFound, "SocialProviderService.GetByID.ValidateNotExist")
	}

	return res, nil
}

func (s *SocialProviderService) Delete(ctx context.Context, id string, nexusData riot.Nexus) (bool, error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("SocialProviderService.Service", "Delete"))

	webservice := riot.GetNexusWebservice(nexusData)

	filter := map[string]interface{}{
		"id":      id,
		"profile": webservice.ID,
	}

	res, err := s.repo.GetByFilter(ctx, filter)

	if err != nil {
		return false, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "SocialProviderService.Service.Delete.s.repo.GetByID")
	}

	if err = res.ValidateNotExist(); err != nil {
		return false, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeNotFound, "SocialProviderService.Service.Delete.ValidateNotExist")
	}

	err = s.repo.SoftDelete(ctx, id)

	return true, err
}

func (s *SocialProviderService) GetAllWithPagination(ctx context.Context, urlValues url.Values, nexusData riot.Nexus) (_ internal.SocialProviderPagin, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("SocialProviderService", "GetAllWithPagination"))

	profile := riot.GetNexusProfile(nexusData)

	if !s.cb.Ready() {
		return internal.SocialProviderPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	// pagination
	paginationQry, _ := riot.QueryByPrefix(&urlValues, "pagination")
	sortQry, _ := riot.QueryByPrefix(&urlValues, "sort")
	dateRangeQry, _ := riot.QueryByPrefix(&urlValues, "date_range")
	statusQry, _ := url.QueryUnescape(urlValues.Get("status"))

	builder := mongorm.NewBuilder()

	filterMap, ok := builder.Filter.(bson.M)
	if !ok {
		filterMap = make(map[string]interface{})
	}

	filterMap["profile"] = profile.ID

	// default value, if not set for pagination
	builder.BuildPagination(s.util.Logger, paginationQry)

	// default value, if not set for sort
	builder.BuildSort(s.util.Logger, sortQry)

	// default value, if not set range date
	builder.BuildRangeDate(s.util.Logger, dateRangeQry)

	// default value, if not set status
	builder.BuildStatus(s.util.Logger, statusQry)

	res, err := s.repo.GetAllWithPagination(ctx, *builder)
	if err != nil {
		return internal.SocialProviderPagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "SocialProviderService.repo.GetAllWithPagination")
	}

	return res, nil
}
