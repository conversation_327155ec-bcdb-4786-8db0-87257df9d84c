package services

import (
	"context"
	"errors"
	"fmt"
	"net/url"
	"strconv"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/mongorm"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/continue-team/hecarim/internal"
	"github.com/continue-team/hecarim/pkg"
	"github.com/continue-team/riot/pkg/logger"
	"github.com/mercari/go-circuitbreaker"
	"go.uber.org/zap"
)

type WebserviceRepository interface {
	Create(ctx context.Context, params internal.CreateWebService) (internal.WebService, error)
	SoftDelete(ctx context.Context, id string) error
	Update(ctx context.Context, id string, params internal.CreateWebService) (internal.WebService, error)
	GetByID(ctx context.Context, id string) (internal.WebService, error)
	GetByFilter(ctx context.Context, filter map[string]interface{}) (internal.WebService, error)
	GetAllWithPagination(ctx context.Context, paginOpt mongorm.FilterOpt) (internal.WebServicePagin, error)
	GetAll(ctx context.Context, filter map[string]interface{}) ([]internal.WebService, error)
}

type WebserviceService struct {
	repo        WebserviceRepository
	profileRepo UserRepository
	cb          *circuitbreaker.CircuitBreaker
	util        riot.Util
	redisCache  *internal.Redis
}

func NewWebserviceService(util riot.Util, repo WebserviceRepository, profileRepo UserRepository, redisCache *internal.Redis) *WebserviceService {
	return &WebserviceService{
		repo:        repo,
		profileRepo: profileRepo,
		util:        util,
		redisCache:  redisCache,
		cb: circuitbreaker.New(
			circuitbreaker.WithOpenTimeout(time.Second*30),
			circuitbreaker.WithTripFunc(circuitbreaker.NewTripFuncConsecutiveFailures(3)),
			circuitbreaker.WithOnStateChangeHookFn(func(oldState, newState circuitbreaker.State) {
				util.Logger.Info("state changed",
					zap.String("oldState", string(oldState)),
					zap.String("newState", string(newState)),
				)
			}),
		),
	}
}

func (s *WebserviceService) Create(ctx context.Context, params internal.CreateWebService, nexusData riot.Nexus) (_ internal.WebService, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("WebserviceService", "Create"))

	if !s.cb.Ready() {
		return internal.WebService{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	var actionDate string

	profile := riot.GetNexusProfile(nexusData)
	client := riot.GetNexusClient(nexusData)

	// if role not allow send bad request.
	// if profile.Role > riot.RoleCompany {
	// 	return internal.WebService{}, riot.WrapErrorfLog(s.util.Logger, errors.New("role not allowed"), riot.ErrorCodeInvalidArgument, "WebserviceService.profile.Role")
	// }

	actionDate = time.Now().Format(pkg.DateLayoutDefault)

	parsedTime, err := pkg.ParseDateString(actionDate, pkg.DateLayoutDefault)
	if err != nil {
		return internal.WebService{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "WebserviceService.Service.ParseActionDate")
	}

	webserviceName := fmt.Sprintf("%s:%d", profile.Username, parsedTime.Unix())

	expireStr := s.util.Conf.Get("APP_ID_EXPIRE")
	expireInt, err := strconv.Atoi(expireStr)
	if err != nil {
		return internal.WebService{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "Webservice.Service.Atoi")
	}

	version := 5
	statusDefault := false
	validUntil := pkg.SetExpireInYears(expireInt)
	isTelegram := params.Telegram

	params.Profile = profile.ID
	params.Name = &webserviceName
	params.Version = &version
	params.Status = &statusDefault
	params.ValidUntil = &validUntil

	if err := params.Validate(); err != nil {
		return internal.WebService{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "WebserviceService.params.Validate")
	}

	res, err := s.repo.Create(ctx, params)
	if err != nil {
		return internal.WebService{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "WebserviceService.s.repo.Create")
	}

	//generate token
	paramsGenerateToken := internal.CreateTokenWebservice{
		WebserviceID: res.ID,
		Util:         s.util,
		Profile: func() internal.Profile {
			if profile.ID != "" {
				return internal.Profile{
					ID:        profile.ID,
					ParentID:  profile.ParentID,
					Username:  profile.Username,
					LoginID:   profile.LoginID,
					Role:      profile.Role,
					CreatedIP: client.IP,
				}
			}
			return internal.Profile{}
		}(),
		IsTelegram: isTelegram,
		ActionDate: actionDate,
		ValidUntil: validUntil,
	}

	token, err := paramsGenerateToken.GenerateToken()
	if err != nil {
		return internal.WebService{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "Webservice.Service.GenerateToken")
	}

	statusActive := true
	payloadUpdate := internal.CreateWebService{
		Telegram:  isTelegram,
		AppID:     &token.Jwt,
		AppSecret: &token.Secret,
		Status:    &statusActive,
	}

	resWebservice, err := s.repo.Update(ctx, res.ID, payloadUpdate)
	if err != nil {
		return internal.WebService{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "WebserviceService.s.repo.Update")
	}

	//set to redis
	if err := s.redisCache.SetWebServiceToRedis(ctx, profile.ID, res.ID, &resWebservice, 24*time.Hour); err != nil {
		return internal.WebService{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "WebserviceService.s.redisCache.SetWebServiceToRedis")
	}

	return resWebservice, nil
}

func (s *WebserviceService) CreateCommand(ctx context.Context, params internal.CreateWebServiceCommand) (internal.WebService, error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("WebserviceService", "Create"))

	var actionDate string

	if err := params.Validate(); err != nil {
		return internal.WebService{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeInvalidArgument, "WebserviceService.params.Validate")
	}

	actionDate = time.Now().Format(pkg.DateLayoutDefault)

	parsedTime, err := pkg.ParseDateString(actionDate, pkg.DateLayoutDefault)
	if err != nil {
		return internal.WebService{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "WebserviceService.Service.ParseActionDate")
	}

	webserviceName := fmt.Sprintf("%s:%d", params.Username, parsedTime.Unix())

	expireStr := s.util.Conf.Get("APP_ID_EXPIRE")
	expireInt, err := strconv.Atoi(expireStr)
	if err != nil {
		return internal.WebService{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "Webservice.Service.Atoi")
	}

	version := 5
	statusDefault := false
	validUntil := pkg.SetExpireInYears(expireInt)
	isTelegram := params.Telegram

	params.Profile = params.Profile

	profile, err := s.profileRepo.GetByID(ctx, params.Profile)
	if err != nil {
		return internal.WebService{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "WebserviceService.s.profileRepo.GetByID")
	}
	params.Name = &webserviceName
	params.Version = &version
	params.Status = &statusDefault
	params.ValidUntil = &validUntil

	paramsWebService := internal.CreateWebService{
		Profile:     profile.ID,
		Name:        params.Name,
		Description: params.Description,
		AppID:       params.AppID,
		AppSecret:   params.AppSecret,
		Version:     params.Version,
		Telegram:    params.Telegram,
		Status:      params.Status,
		ValidUntil:  params.ValidUntil,
	}

	res, err := s.repo.Create(ctx, paramsWebService)
	if err != nil {
		return internal.WebService{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "WebserviceService.s.repo.Create")
	}

	//generate token
	paramsGenerateToken := internal.CreateTokenWebservice{
		Profile: internal.Profile{
			ID:        profile.ID,
			Username:  profile.Username,
			LoginID:   profile.LoginID,
			Role:      profile.Role,
			CreatedIP: profile.CreatedIP,
		},
		WebserviceID: res.ID,
		Util:         s.util,
		IsTelegram:   isTelegram,
		ActionDate:   actionDate,
		ValidUntil:   validUntil,
	}

	token, err := paramsGenerateToken.GenerateToken()
	if err != nil {
		return internal.WebService{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "Webservice.Service.GenerateToken")
	}

	statusActive := true
	payloadUpdate := internal.CreateWebService{
		Telegram:  isTelegram,
		AppID:     &token.Jwt,
		AppSecret: &token.Secret,
		Status:    &statusActive,
	}

	resWebservice, err := s.repo.Update(ctx, res.ID, payloadUpdate)
	if err != nil {
		return internal.WebService{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "WebserviceService.s.repo.Update")
	}

	//set to redis
	// if err := s.redisCache.SetWebServiceToRedis(ctx, profile.ID, res.ID, &resWebservice, 24*time.Hour); err != nil {
	// 	return internal.WebService{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "WebserviceService.s.redisCache.SetWebServiceToRedis")
	// }

	return resWebservice, nil
}

func (s *WebserviceService) Update(ctx context.Context, id string, params internal.CreateWebService, nexusData riot.Nexus) (_ internal.WebService, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("WebserviceService", "Update"))

	if !s.cb.Ready() {
		return internal.WebService{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	res, err := s.repo.Update(ctx, id, params)
	if err != nil {
		return internal.WebService{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "WebserviceService.s.repo.Update")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.WebService{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeNotFound, "WebserviceService.Update.ValidateNotExist")
	}

	return res, nil
}

func (s *WebserviceService) GetByID(ctx context.Context, id string, includes []string, nexusData riot.Nexus) (_ internal.WebService, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("WebserviceService", "GetByID"))

	if !s.cb.Ready() {
		return internal.WebService{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	res, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return internal.WebService{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "WebserviceService.s.repo.GetByID")
	}

	if err := res.ValidateNotExist(); err != nil {
		return internal.WebService{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeNotFound, "WebserviceService.GetByID.ValidateNotExist")
	}

	return res, nil
}

func (s *WebserviceService) ValidateToken(ctx context.Context, token string, nexusData riot.Nexus) (bool, error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("WebserviceService.Service", "ValidateToken"))

	profile := riot.GetNexusProfile(nexusData)
	webservice := riot.GetNexusWebservice(nexusData)

	//check redis
	resRedis, err := s.redisCache.GetWebServiceFromRedis(ctx, profile.ID, webservice.ID)

	if err != nil {
		return false, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "WebserviceService.Service.s.redisCache.GetWebserviceFromRedis")
	}

	if resRedis != nil {
		if resRedis.AppID != token {
			return false, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "WebserviceService.Service.s.redisCache.TokenNotMatch")
		}
		return true, nil
	}

	filter := map[string]interface{}{
		"profile": profile.ID,
		"id":      webservice.ID,
		"app_id":  token,
	}

	res, err := s.repo.GetByFilter(ctx, filter)

	if err != nil {
		return false, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "WebserviceService.Service.s.repo.GetByFilter")
	}

	if err := res.ValidateNotExist(); err != nil {
		return false, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeNotFound, "WebserviceService.Service.ValidateNotExist")
	}

	//set cache redis
	err = s.redisCache.SetWebServiceToRedis(ctx, profile.ID, res.ID, &res, 24*time.Hour)

	if err != nil {
		return false, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "Webservice.Service.s.redisCache.SetWebserviceToRedis")
	}

	return true, nil

}

func (s *WebserviceService) Verify(ctx context.Context, token string, nexusData riot.Nexus) (bool, error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("WebserviceService.Service", "Verify"))

	if token == "" {
		return false, riot.WrapErrorfLog(s.util.Logger, errors.New("token empty"), riot.ErrorCodeInvalidArgument, "WebserviceService.Service.Verify")
	}

	filter := map[string]interface{}{
		"app_id": token,
	}

	webservice, err := s.repo.GetByFilter(ctx, filter)

	if err != nil {
		return false, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "WebserviceService.Service.s.repo.GetByID")
	}

	if err = webservice.ValidateNotExist(); err != nil {
		return false, nil
	}

	return true, nil
}

func (s *WebserviceService) Revoke(ctx context.Context, id string, nexusData riot.Nexus) (bool, error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("WebserviceService.Service", "Revoke"))

	webservice, err := s.repo.GetByID(ctx, id)

	if err != nil {
		return false, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "WebserviceService.Service.s.repo.GetByID")
	}

	if err = webservice.ValidateNotExist(); err != nil {
		return false, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeNotFound, "WebserviceService.Service.ValidateNotExist")
	}

	err = s.repo.SoftDelete(ctx, id)

	return true, err
}

func (s *WebserviceService) GetAllWithPagination(ctx context.Context, urlValues url.Values, nexusData riot.Nexus) (_ internal.WebServicePagin, err error) {
	// define Logger
	s.util.Logger = logger.FromCtx(ctx).With(zap.String("WebserviceService", "GetAllWithPagination"))

	profile := riot.GetNexusProfile(nexusData)

	if !s.cb.Ready() {
		return internal.WebServicePagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "service not available")
	}

	defer func() {
		err = s.cb.Done(ctx, err)
	}()

	// pagination
	paginationQry, _ := riot.QueryByPrefix(&urlValues, "pagination")
	sortQry, _ := riot.QueryByPrefix(&urlValues, "sort")
	dateRangeQry, _ := riot.QueryByPrefix(&urlValues, "date_range")
	statusQry, _ := url.QueryUnescape(urlValues.Get("status"))

	builder := mongorm.NewBuilder()

	filterMap, ok := builder.Filter.(bson.M)
	if !ok {
		filterMap = make(map[string]interface{})
	}

	filterMap["profile"] = profile.ID

	// default value, if not set for pagination
	builder.BuildPagination(s.util.Logger, paginationQry)

	// default value, if not set for sort
	builder.BuildSort(s.util.Logger, sortQry)

	// default value, if not set range date
	builder.BuildRangeDate(s.util.Logger, dateRangeQry)

	// default value, if not set status
	builder.BuildStatus(s.util.Logger, statusQry)

	res, err := s.repo.GetAllWithPagination(ctx, *builder)
	if err != nil {
		return internal.WebServicePagin{}, riot.WrapErrorfLog(s.util.Logger, err, riot.ErrorCodeUnknown, "WebserviceService.repo.GetAllWithPagination")
	}

	return res, nil
}
