package cmd

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/continue-team/hecarim/internal"
	"github.com/continue-team/hecarim/internal/services"
	"github.com/spf13/cobra"
)

func WebServiceCommand(svc *services.WebserviceService) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "webservice",
		Short: "Parses a JSON input",
		RunE: func(cmd *cobra.Command, args []string) error {
			fmt.Println("✅ Command 'webservice' is running...") // Debugging

			data, _ := cmd.Flags().GetString("data")
			fmt.Println("Received data:", data) // Debugging

			if data == "" {
				fmt.Println("⚠️ No data received")
				return fmt.Errorf("missing --data parameter")
			}

			var input internal.CreateWebServiceCommand
			if err := json.Unmarshal([]byte(data), &input); err != nil {
				fmt.Println("🚨 Error parsing JSON:", err)
				return fmt.Errorf("error parsing JSON: %w", err)
			}

			fmt.Println("Creating web service...", input) // Debugging

			_, err := svc.CreateCommand(context.Background(), input)
			if err != nil {
				fmt.Println("🚨 Error creating web service:", err)
				return err
			}

			fmt.Println("✅ Web service created successfully")
			return nil
		},
	}

	cmd.Flags().StringP("data", "d", "", "JSON input data")
	cmd.MarkFlagRequired("data")

	return cmd
}
