// constant.go
package constant

// Status constants
const (
	StatusNexusInactive = "Inactive"
	StatusNexusDraft    = "Draft"
	StatusNexusLive     = "Live"
	StatusDelete        = "delete"
	StatusActive        = "active"
	StatusInactive      = "inactive"
	StatusPending       = "pending"
	StatusBuildProses   = "build_proses"
	StatusBuildSuccess  = "build_success"
	StatusBuildPending  = "pending"
)

// TransactionType constants
const (
	TypeNexusInactive = 0
	TypeNexusDraft    = 1
	TypeNexusLive     = 2
	TypeActive        = 99
	TypeInactive      = 98
	TypeDelete        = 97
	TypeBuildSuccess  = 96
	TypeBuildProses   = 95
	TypeBuildPending  = 94
)

// statusMapping is a general-purpose mapping between status names and their corresponding constants
var statusMapping = map[string]int{
	StatusNexusInactive: TypeNexusInactive,
	StatusNexusDraft:    TypeNexusDraft,
	StatusNexusLive:     TypeNexusLive,
}

// Constant function to get the constant value based on the key
func Constant(key string) int {
	if v, ok := statusMapping[key]; ok {
		return v
	}

	return -1
}

// ReverseConstant function to get the status name based on the constant value
func ReverseConstant(value int) string {
	// Populate reverseStatusMapping by iterating over statusMapping
	reverseStatusMapping := make(map[int]string)
	for key, val := range statusMapping {
		reverseStatusMapping[val] = key
	}

	if key, ok := reverseStatusMapping[value]; ok {
		return key
	}
	return "Not Found"
}

const (
	// Route Event
	TaskEvtProfileRegistered = "register"
	TaskEvtAccessLog         = "access_log"
	TaskEvtUpdateAccessLog   = "update_access_log"

	// Task Event
	TaskEvtCreateMerchant = "event:create_merchant"
	TaskEvtUpdateMerchant = "event:update_merchant"
)

const (
	StateAuth    = "auth"
	StateConnect = "connect"
)
