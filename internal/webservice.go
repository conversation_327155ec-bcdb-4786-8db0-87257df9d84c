package internal

import (
	"fmt"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/pkg/util"
	validation "github.com/go-ozzo/ozzo-validation/v4"
)

type WebService struct {
	ID          string     `json:"_id"`
	Profile     string     `json:"profile"`
	Name        string     `json:"name"`
	Description string     `json:"description"`
	AppID       string     `json:"app_id"`
	AppSecret   string     `json:"app_secret"`
	Version     int        `json:"version"`
	Telegram    bool       `json:"telegram"`
	Status      bool       `json:"status"`
	ValidUntil  *time.Time `json:"valid_until"`
	CreatedAt   *time.Time `json:"created_at"`
	UpdatedAt   *time.Time `json:"updated_at"`
}

type WebServicePagin struct {
	Limit        int          `json:"limit"`
	Page         int          `json:"page"`
	Sort         string       `json:"sort"`
	TotalRecords int          `json:"total_records"`
	TotalPages   int          `json:"total_pages"`
	Records      []WebService `json:"records"`
}

func (f WebService) ValidateExist() error {
	if f.ID != "" {
		return validation.Errors{
			util.GetStructName(f): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already taken.", f.Name)),
		}
	}
	return nil
}

func (f WebService) ValidateNotExist() error {
	if f.ID == "" {
		return validation.Errors{
			util.GetStructName(f): riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", f.ID)),
		}
	}
	return nil
}

func (f *WebService) ResponseData() interface{} {
	if f.ID == "" {
		return map[string]interface{}{}
	} else {
		return f
	}
}

type CreateWebServiceCommand struct {
	Username    string  `json:"username,omitempty"`
	Profile     string  `json:"profile,omitempty"`
	Name        *string `json:"name,omitempty"`
	Description *string `json:"description,omitempty"`
	AppID       *string `json:"app_id,omitempty"`
	AppSecret   *string `json:"app_secret,omitempty"`
	Version     *int    `json:"version,omitempty"`
	Telegram    *bool   `json:"telegram,omitempty"`
	Status      *bool   `json:"status,omitempty"`
	ValidUntil  *string `json:"valid_until,omitempty"`
}

func (c CreateWebServiceCommand) Validate() error {
	if err := validation.ValidateStruct(&c,
		validation.Field(&c.Profile, validation.Required),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}

type CreateWebService struct {
	Profile     string  `json:"profile,omitempty"`
	Name        *string `json:"name,omitempty"`
	Description *string `json:"description,omitempty"`
	AppID       *string `json:"app_id,omitempty"`
	AppSecret   *string `json:"app_secret,omitempty"`
	Version     *int    `json:"version,omitempty"`
	Telegram    *bool   `json:"telegram,omitempty"`
	Status      *bool   `json:"status,omitempty"`
	ValidUntil  *string `json:"valid_until,omitempty"`
}

func (c CreateWebService) Validate() error {
	if err := validation.ValidateStruct(&c,
		validation.Field(&c.Description, validation.Required),
		validation.Field(&c.Profile, validation.Required),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}

type QueryWebService struct {
	Name string `json:"name,omitempty"`
}

type UpdateWebservice struct {
	AppID     string `json:"app_id,omitempty"`
	AppSecret string `json:"app_secret,omitempty"`
	Telegram  bool   `json:"telegram"`
}
