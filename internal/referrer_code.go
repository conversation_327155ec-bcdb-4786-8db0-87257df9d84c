package internal

import (
	"fmt"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/pkg/util"
	validation "github.com/go-ozzo/ozzo-validation/v4"
)

type ReferrerCode struct {
	ID          string     `json:"_id"`
	Profile     string     `json:"profile"`
	Code        string     `json:"code"`
	Description string     `json:"description"`
	Status      bool       `json:"status"`
	ValidUntil  *time.Time `json:"valid_until"`
	CreatedAt   *time.Time `json:"created_at"`
	UpdatedAt   *time.Time `json:"updated_at"`
}

type ReferrerCodeValidate struct {
	Validate    bool       `json:"validate"`
	Profile     string     `json:"profile,omitempty"`
	Code        string     `json:"code,omitempty"`
	Description string     `json:"description,omitempty"`
	ValidUntil  *time.Time `json:"valid_until,omitempty"`
}

func (f ReferrerCodeValidate) CheckValidate() error {
	if !f.Validate {
		return validation.Errors{
			"Referrer code": riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%snot valid.", f.Code)),
		}
	}
	return nil
}

type ReferrerCodePagin struct {
	Limit        int            `json:"limit"`
	Page         int            `json:"page"`
	Sort         string         `json:"sort"`
	TotalRecords int            `json:"total_records"`
	TotalPages   int            `json:"total_pages"`
	Records      []ReferrerCode `json:"records"`
}

func (f ReferrerCode) ValidateExist() error {
	if f.ID != "" {
		return validation.Errors{
			util.GetStructName(f): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already taken.", f.Code)),
		}
	}
	return nil
}

func (f ReferrerCode) ValidateNotExist() error {
	if f.ID == "" {
		return validation.Errors{
			util.GetStructName(f): riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", f.ID)),
		}
	}
	return nil
}

func (f *ReferrerCode) ResponseData() interface{} {
	if f.ID == "" {
		return map[string]interface{}{}
	} else {
		return f
	}
}

type CreateReferrerCode struct {
	Profile     string  `json:"profile,omitempty"`
	Code        *string `json:"code,omitempty"`
	Description *string `json:"description,omitempty"`
	Status      *bool   `json:"status,omitempty"`
	ValidUntil  *string `json:"valid_until,omitempty"`
}

func (c CreateReferrerCode) Validate() error {
	if err := validation.ValidateStruct(&c,
		validation.Field(&c.Code, validation.Required),
		validation.Field(&c.Profile, validation.Required),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}

type QueryReferrerCode struct {
	Name string `json:"name,omitempty"`
}

type UpdateReferrerCode struct {
	AppID     string `json:"app_id,omitempty"`
	AppSecret string `json:"app_secret,omitempty"`
	Telegram  bool   `json:"telegram"`
}
