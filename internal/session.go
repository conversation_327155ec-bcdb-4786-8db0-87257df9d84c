package internal

import (
	"fmt"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/pkg/util"
	validation "github.com/go-ozzo/ozzo-validation/v4"
)

type Session struct {
	ID            string     `json:"_id"`
	Parent        string     `json:"parent"`
	User          string     `json:"user"`
	Username      string     `json:"username"`
	LoginID       string     `json:"login_id"`
	Session       string     `json:"session"`
	Guest         bool       `json:"guest"`
	Online        bool       `json:"online"`
	HistoricalMtc string     `json:"historical_mtc"`
	RecentMtc     string     `json:"recent_mtc"`
	CreatedIP     string     `json:"created_ip"`
	ValidUntil    *time.Time `json:"valid_until"`
	LastAct       *time.Time `json:"last_act"`
	CreatedAt     *time.Time `json:"created_at"`
	UpdatedAt     *time.Time `json:"updated_at"`
}

func (s Session) Validate() error {

	if !s.Online {
		return validation.Errors{
			util.GetStructName(s): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s not online.", util.GetStructName(s))),
		}
	}

	if s.ValidUntil != nil {
		if time.Now().After(*s.ValidUntil) {
			return validation.Errors{
				util.GetStructName(s): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s expired.", util.GetStructName(s))),
			}
		}
	}

	return nil
}

type SessionPagin struct {
	Limit        int       `json:"limit"`
	Page         int       `json:"page"`
	Sort         string    `json:"sort"`
	TotalRecords int       `json:"total_records"`
	TotalPages   int       `json:"total_pages"`
	Records      []Session `json:"records"`
}

func (f Session) ValidateExist() error {
	if f.ID != "" {
		return validation.Errors{
			util.GetStructName(f): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already taken.", f.ID)),
		}
	}
	return nil
}

func (f Session) ValidateNotExist() error {
	if f.ID == "" {
		return validation.Errors{
			util.GetStructName(f): riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", f.ID)),
		}
	}
	return nil
}

func (f *Session) ResponseData() interface{} {
	if f.ID == "" {
		return map[string]interface{}{}
	} else {
		return f
	}
}

type CreateSession struct {
	Parent        string  `json:"parent,omitempty"`
	User          string  `json:"user,omitempty"`
	Username      *string `json:"username,omitempty"`
	LoginID       *string `json:"login_id,omitempty"`
	Session       *string `json:"session,omitempty"`
	Guest         *bool   `json:"guest,omitempty"`
	Online        *bool   `json:"online,omitempty"`
	HistoricalMtc *string `json:"historical_mtc,omitempty"`
	RecentMtc     *string `json:"recent_mtc,omitempty"`
	CreatedIP     *string `json:"created_ip,omitempty"`
	ValidUntil    *string `json:"valid_until,omitempty"`
	LastAct       *string `json:"last_act,omitempty"`
	Forever       bool    `json:"forever,omitempty"`
}

func (c CreateSession) Validate() error {
	if err := validation.ValidateStruct(&c,
		validation.Field(&c.User, validation.Required),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}

type QuerySession struct {
	Name string `json:"name,omitempty"`
}

type IdentifierSession struct {
	Session string `json:"session,omitempty"`
}

func (c IdentifierSession) Validate() error {
	if err := validation.ValidateStruct(&c,
		validation.Field(&c.Session, validation.Required),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}
