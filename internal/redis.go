package internal

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/continue-team/riot"
	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"
)

func HKeyFormat(format string, args ...interface{}) string {
	return fmt.Sprintf(format, args...)
}

func getWebServiceMapKey(profile string, subject string) string {
	return HKeyFormat("WebService:%s:DETAIL:%s", profile, subject)
}

func getSessionProfileMapKey(profile string) string {
	return HKeyFormat("PROFILE:%s:SESSION", profile)
}

func getSessionAuth(profile string, authState string) string {
	return HKeyFormat("PROFILE:%s:STATE:%s", profile, authState)
}

func getProfileMapKey(profile string) string {
	return HKeyFormat("PROFILE:%s:DETAIL", profile)
}

func getGlossaryCompaniesMapKey() string {
	return "GLOSSARY:COMPANIES"
}

type Redis struct {
	client *redis.Client
	logger *zap.Logger
}

func NewRedis(util riot.Util) *Redis {
	return &Redis{
		client: util.Redis,
		logger: util.Logger,
	}
}

func (r *Redis) GetJsonDataByKey(ctx context.Context, key string) (map[string]interface{}, error) {

	value, err := r.client.Get(ctx, key).Result()
	if err != nil {
		// Check if the error is due to the key not existing (nil value)
		if err == redis.Nil {
			// Handle case where key does not exist (nil value)
			return nil, nil // Or return any default value you want
		}
		return nil, riot.WrapErrorfLog(r.logger, err, riot.ErrorCodeUnknown, "Redis.GetJsonDataByKey.r.client.Get")
	}

	var data map[string]interface{}
	if err := json.Unmarshal([]byte(value), &data); err != nil {
		return nil, riot.WrapErrorfLog(r.logger, err, riot.ErrorCodeUnknown, "Redis.GetJsonDataByKey.json.Unmarshal")
	}

	return data, nil
}

func (r *Redis) SetJsonDataByKey(ctx context.Context, key string, data map[string]interface{}) error {

	value, err := json.Marshal(data)
	if err != nil {
		return err
	}

	return r.client.Set(ctx, key, value, 0).Err()
}

func (r *Redis) DeleteJsonDataByKey(ctx context.Context, key string) error {
	return r.client.Del(ctx, key).Err()
}

func (r *Redis) SetWebServiceToRedis(ctx context.Context, profileID, WebServiceID string, data *WebService, ttl time.Duration) error {

	dataKey := getWebServiceMapKey(profileID, WebServiceID)

	r.logger.Info("Redis.SetWebServiceToRedis.getWebServiceMapKey", zap.String("dataKey", dataKey))

	jsonData, err := json.Marshal(data)
	if err != nil {
		return riot.WrapErrorfLog(r.logger, err, riot.ErrorCodeUnknown, "Redis.SetWebServiceToRedis.json.Marshal")
	}

	err = r.client.Set(ctx, dataKey, jsonData, ttl).Err()
	if err != nil {
		return riot.WrapErrorfLog(r.logger, err, riot.ErrorCodeUnknown, "Redis.SetWebServiceToRedis.r.client.Set")
	}

	return nil
}

func (r *Redis) GetWebServiceFromRedis(ctx context.Context, profileID, WebServiceID string) (*WebService, error) {

	dataKey := getWebServiceMapKey(profileID, WebServiceID)

	r.logger.Info("Redis.GetWebServiceFromRedis.getWebServiceMapKey", zap.String("dataKey", dataKey))

	data, err := r.GetJsonDataByKey(ctx, dataKey)
	if err != nil {
		return nil, riot.WrapErrorfLog(r.logger, err, riot.ErrorCodeUnknown, "Redis.GetWebServiceFromRedis.r.GetJsonDataByKey")
	}

	if data == nil {
		return nil, nil
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		return nil, riot.WrapErrorfLog(r.logger, err, riot.ErrorCodeUnknown, "Redis.GetWebServiceFromRedis.json.Marshal")
	}

	var responseDetail WebService
	if err := json.Unmarshal(jsonData, &responseDetail); err != nil {
		return nil, riot.WrapErrorfLog(r.logger, err, riot.ErrorCodeUnknown, "Redis.GetWebServiceFromRedis.json.Unmarshal")
	}

	return &responseDetail, nil
}

func (r *Redis) SetSessionProfileFromRedis(ctx context.Context, profileID string, data *Session, ttl time.Duration) error {

	dataKey := getSessionProfileMapKey(profileID)

	r.logger.Info("Redis.SetSessionProfileFromRedis.getSessionProfileMapKey", zap.String("dataKey", dataKey))

	jsonData, err := json.Marshal(data)
	if err != nil {
		return riot.WrapErrorfLog(r.logger, err, riot.ErrorCodeUnknown, "Redis.SetSessionProfileFromRedis.json.Marshal")
	}

	err = r.client.Set(ctx, dataKey, jsonData, ttl).Err()
	if err != nil {
		return riot.WrapErrorfLog(r.logger, err, riot.ErrorCodeUnknown, "Redis.SetSessionProfileFromRedis.r.client.Set")
	}

	return nil
}

func (r *Redis) SetAuthSession(ctx context.Context, profileID, stateAuth, value string) error {

	dataKey := getSessionAuth(profileID, stateAuth)

	r.logger.Info("Redis.SetAuthSession.getSessionProfileMapKey", zap.String("dataKey", dataKey))

	// Simpan value di Redis menggunakan kunci dataKey
	err := r.client.Set(ctx, dataKey, value, 0).Err()
	if err != nil {
		r.logger.Error("Failed to set auth session in Redis", zap.Error(err))
		return err
	}

	return nil
}

func (r *Redis) GetAuthSession(ctx context.Context, profileID, stateAuth string) (*string, error) {
	dataKey := getSessionAuth(profileID, stateAuth)

	r.logger.Info("Redis.GetAuthSession.getSessionProfileMapKey", zap.String("dataKey", dataKey))

	value, err := r.client.Get(ctx, dataKey).Result()
	if err == redis.Nil {
		return nil, nil
	} else if err != nil {
		r.logger.Error("Failed to get auth session from Redis", zap.Error(err))
		return nil, err
	}

	return &value, nil
}

func (r *Redis) ValidateStateSession(ctx context.Context, profileID, stateAuth string) (bool, error) {

	exist, err := r.GetAuthSession(ctx, profileID, stateAuth)

	if err != nil {
		return false, err
	}

	if exist == nil {
		return false, err
	}

	return true, nil
}
