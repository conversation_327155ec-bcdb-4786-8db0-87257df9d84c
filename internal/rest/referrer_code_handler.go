package rest

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strings"

	"github.com/continue-team/hecarim/internal"
	"github.com/continue-team/riot"
	common "github.com/continue-team/riot/pkg"

	"github.com/continue-team/riot/pkg/logger"
	"github.com/gorilla/mux"
	"go.uber.org/zap"
)

type ReferrerCodeSvc interface {
	Create(ctx context.Context, params internal.CreateReferrerCode, nexusData riot.Nexus) (_ internal.ReferrerCode, err error)
	Update(ctx context.Context, id string, params internal.CreateReferrerCode, nexusData riot.Nexus) (_ internal.ReferrerCode, err error)
	GetByID(ctx context.Context, id string, includes []string, nexusData riot.Nexus) (_ internal.ReferrerCode, err error)
	GetAllWithPagination(ctx context.Context, urlValues url.Values, nexusData riot.Nexus) (_ internal.ReferrerCodePagin, err error)
	ValidateCode(ctx context.Context, referrerCode string, nexusData riot.Nexus) (_ internal.ReferrerCodeValidate, err error)
	Delete(ctx context.Context, id string, nexusData riot.Nexus) (err error)
}

type ReferrerCodeHandler struct {
	svc  ReferrerCodeSvc
	util riot.Util
}

func NewReferrerCodeHandler(utl riot.Util, svc ReferrerCodeSvc) *ReferrerCodeHandler {
	return &ReferrerCodeHandler{
		svc:  svc,
		util: utl,
	}
}

// Register connects the handlers to the router.
func (h *ReferrerCodeHandler) Register(r *mux.Router) {

	v1 := r.PathPrefix("/v1").Subrouter()

	v1.HandleFunc("/referrer-codes", h.Create).Methods(http.MethodPost)
	v1.HandleFunc(fmt.Sprintf("/referrer-codes/verify/{code:%s}", "[a-zA-Z0-9-]+"), h.Verify).Methods(http.MethodGet)
	v1.HandleFunc(fmt.Sprintf("/referrer-codes/{id:%s}", common.ObjectIDRegex), h.GetByID).Methods(http.MethodGet)
	v1.HandleFunc(fmt.Sprintf("/referrer-codes/{id:%s}", common.ObjectIDRegex), h.Delete).Methods(http.MethodDelete)
	v1.HandleFunc("/referrer-codes", h.GetAll).Methods(http.MethodGet)
}

func (h *ReferrerCodeHandler) Create(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("ReferrerCodeHandler", "create"))

	requestID := r.Header.Get("X-Request-ID")

	var body internal.CreateReferrerCode

	// decode request body
	if err := riot.DecodeRequestBody(r, w, &body); err != nil {
		h.util.Logger.Error(err.Error())
		return
	}

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	referrerCode, err := h.svc.Create(r.Context(), body, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)

		return
	}

	riot.ResponseOK(w, referrerCode.ResponseData())
}

func (h *ReferrerCodeHandler) GetByID(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("ReferrerCodeHandler", "getByID"))

	requestID := r.Header.Get("X-Request-ID")

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	var includes []string
	id := mux.Vars(r)["id"] //nolint: gosimple
	includeQuery := r.URL.Query().Get("includes")

	if includeQuery != "" {
		includes = strings.Split(includeQuery, ",")
	}

	referrerCode, err := h.svc.GetByID(r.Context(), id, includes, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, referrerCode.ResponseData())
}

func (h *ReferrerCodeHandler) GetAll(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("ReferrerCodeHandler", "GetAll"))

	requestID := r.Header.Get("X-Request-ID")

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	urlValues := r.URL.Query()

	referrerCode, err := h.svc.GetAllWithPagination(r.Context(), urlValues, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, referrerCode)
}

func (h *ReferrerCodeHandler) Delete(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("ReferrerCodeHandler", "Delete"))

	requestID := r.Header.Get("X-Request-ID")

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	// NOTE: Safe to ignore error, because it's always defined.
	id := mux.Vars(r)["id"] //nolint: gosimple

	err := h.svc.Delete(r.Context(), id, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, nil, "Data successfully deleted.")
}

func (h *ReferrerCodeHandler) Update(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("ReferrerCodeHandler", "Update"))

	requestID := r.Header.Get("X-Request-ID")

	// NOTE: Safe to ignore error, because it's always defined.
	id := mux.Vars(r)["id"] //nolint: gosimple

	var body internal.CreateReferrerCode
	if err := riot.DecodeRequestBody(r, w, &body); err != nil {
		h.util.Logger.Error(err.Error())
		return
	}

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	referrerCode, err := h.svc.Update(r.Context(), id, body, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)

		return
	}

	riot.ResponseOK(w, referrerCode.ResponseData())
}

func (h *ReferrerCodeHandler) Verify(w http.ResponseWriter, r *http.Request) {

	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("ReferrerCodeHandler", "Verify"))

	requestId := r.Header.Get("X-Request-ID")

	code := mux.Vars(r)["code"]

	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	isVerify, err := h.svc.ValidateCode(r.Context(), code, nexusData)

	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOKWithReqID(w, requestId, isVerify)
}
