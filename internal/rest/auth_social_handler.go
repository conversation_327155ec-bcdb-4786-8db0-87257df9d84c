package rest

import (
	"context"
	"fmt"
	"net/http"

	"firebase.google.com/go/v4/auth"
	"github.com/continue-team/hecarim/internal"
	"github.com/continue-team/hecarim/internal/constant"
	"github.com/continue-team/hecarim/internal/firebaseauth"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/middlewares"
	"github.com/continue-team/riot/pkg/logger"
	"github.com/gorilla/mux"
	"go.uber.org/zap"
)

type AuthSocialSvc interface {
	AuthProvider(ctx context.Context, identifier internal.AuthIdentifier, state string, nexusData riot.Nexus) (internal.DataProfileAuth, error)
	AuthFBProvider(ctx context.Context, identifier internal.AuthIdentifier, userProvider *firebaseauth.FirebaseJWT, state string, nexusData riot.Nexus) (internal.DataProfileAuth, error)
}

type ProviderKey string

type AuthSocialHandler struct {
	svc    AuthSocialSvc
	util   riot.Util
	fbAuth *auth.Client
}

func NewAuthSocialHandler(utl riot.Util, svc AuthSocialSvc, fbAuth *auth.Client) *AuthSocialHandler {
	return &AuthSocialHandler{
		svc:    svc,
		util:   utl,
		fbAuth: fbAuth,
	}
}

// Register connects the handlers to the router.
func (h *AuthSocialHandler) Register(r *mux.Router) {

	v1 := r.PathPrefix("/v1").Subrouter()

	v1.HandleFunc(fmt.Sprintf("/auth/socials/{provider:%s}", "[a-zA-Z0-9-]+"), h.AuthSocial).Methods(http.MethodGet)
	v1.HandleFunc(fmt.Sprintf("/auth/socials/connects/{provider:%s}", "[a-zA-Z0-9-]+"), h.ConnectAuthSocial).Methods(http.MethodGet)
	v1.HandleFunc(fmt.Sprintf("/auth/socials/{provider:%s}/{authState:%s}", "[a-zA-Z0-9-]+", "[a-zA-Z0-9-]+"), h.AuthSocialRegister).Methods(http.MethodPost)
	v1.Handle("/auth/socials/firebase",
		middlewares.ApplyMiddleware(http.HandlerFunc(h.AuthFBProvider),
			firebaseauth.AuthMiddleware(h.fbAuth))).
		Methods(http.MethodPost)
}

func (h *AuthSocialHandler) AuthSocial(w http.ResponseWriter, r *http.Request) {
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("AuthSocialHandler", "AuthSocial"))

	requestID := r.Header.Get("X-Request-ID")

	if requestID == "" {
		nexusData := r.Context().Value(riot.RequestNexusCtxKey).(riot.Nexus)
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	state := r.URL.Query().Get("state")
	code := r.URL.Query().Get("code")
	oauthToken := r.URL.Query().Get("oauth_token")
	oauthVerifier := r.URL.Query().Get("oauth_verifier")
	domain := r.URL.Query().Get("domain")
	isGroup := r.URL.Query().Get("is_group")

	identifier := internal.AuthIdentifier{
		State:         state,
		Code:          code,
		OauthToken:    oauthToken,
		Domain:        domain,
		OauthVerifier: oauthVerifier,
		IsGroup:       isGroup,
		Req:           r,
		Res:           w,
	}

	authUrl, err := h.svc.AuthProvider(r.Context(), identifier, constant.StateAuth, nexusData)

	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, authUrl)
}

func (h *AuthSocialHandler) ConnectAuthSocial(w http.ResponseWriter, r *http.Request) {

	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("AuthSocialHandler", "ConnectAuthSocial"))

	requestID := r.Header.Get("X-Request-ID")

	if requestID == "" {
		nexusData := r.Context().Value(riot.RequestNexusCtxKey).(riot.Nexus)
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	state := r.URL.Query().Get("state")
	code := r.URL.Query().Get("code")
	oauthToken := r.URL.Query().Get("oauth_token")
	oauthVerifier := r.URL.Query().Get("oauth_verifier")
	isGroup := r.URL.Query().Get("is_group")

	identifier := internal.AuthIdentifier{
		State:         state,
		Code:          code,
		OauthToken:    oauthToken,
		OauthVerifier: oauthVerifier,
		IsGroup:       isGroup,
		Req:           r,
		Res:           w,
	}

	authUrl, err := h.svc.AuthProvider(r.Context(), identifier, constant.StateConnect, nexusData)

	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, authUrl)
}

func (h *AuthSocialHandler) AuthSocialRegister(w http.ResponseWriter, r *http.Request) {
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("AuthSocialHandler", "AuthSocialRegister"))

	requestID := r.Header.Get("X-Request-ID")

	if requestID == "" {
		nexusData := r.Context().Value(riot.RequestNexusCtxKey).(riot.Nexus)
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	state := r.URL.Query().Get("state")

	identifier := internal.AuthIdentifier{
		State: state,
		Req:   r,
		Res:   w,
	}

	authUrl, err := h.svc.AuthProvider(r.Context(), identifier, constant.StateAuth, nexusData)

	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, authUrl)
}

func (h *AuthSocialHandler) AuthFBProvider(w http.ResponseWriter, r *http.Request) {
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("AuthSocialHandler", "AuthSocialFirebase"))

	requestID := r.Header.Get("X-Request-ID")

	if requestID == "" {
		nexusData := r.Context().Value(riot.RequestNexusCtxKey).(riot.Nexus)
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	user, ok := firebaseauth.GetFirebaseUserFromContext(r.Context())

	if !ok {
		riot.RenderErrorResponse(r.Context(), w, "user not found", nil)
		return
	}

	var body internal.CreateUser

	// decode request body
	if err := riot.DecodeRequestBody(r, w, &body); err != nil {
		h.util.Logger.Error(err.Error())
		return
	}

	state := mux.Vars(r)["authState"]

	identifier := internal.AuthIdentifier{
		State: state,
		Req:   r,
		Res:   w,
	}

	if body.IsGroup {
		identifier.IsGroup = "true"
	}

	authUrl, err := h.svc.AuthFBProvider(r.Context(), identifier, user, state, nexusData)

	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, authUrl)
}
