package rest

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strings"

	"github.com/continue-team/hecarim/internal"
	"github.com/continue-team/riot"
	common "github.com/continue-team/riot/pkg"

	"github.com/continue-team/riot/pkg/logger"
	"github.com/gorilla/mux"
	"go.uber.org/zap"
)

type WebserviceSvc interface {
	Create(ctx context.Context, params internal.CreateWebService, nexusData riot.Nexus) (_ internal.WebService, err error)
	Revoke(ctx context.Context, id string, nexusData riot.Nexus) (bool, error)
	Update(ctx context.Context, id string, params internal.CreateWebService, nexusData riot.Nexus) (_ internal.WebService, err error)
	GetByID(ctx context.Context, id string, includes []string, nexusData riot.Nexus) (_ internal.WebService, err error)
	GetAllWithPagination(ctx context.Context, urlValues url.Values, nexusData riot.Nexus) (_ internal.WebServicePagin, err error)
	ValidateToken(ctx context.Context, token string, nexusData riot.Nexus) (bool, error)
	Verify(ctx context.Context, token string, nexusData riot.Nexus) (bool, error)
}

type WebserviceHandler struct {
	svc  WebserviceSvc
	util riot.Util
}

func NewWebserviceHandler(utl riot.Util, svc WebserviceSvc) *WebserviceHandler {
	return &WebserviceHandler{
		svc:  svc,
		util: utl,
	}
}

// Register connects the handlers to the router.
func (h *WebserviceHandler) Register(r *mux.Router) {

	v1 := r.PathPrefix("/v1").Subrouter()

	v1.HandleFunc("/webservices", h.Create).Methods(http.MethodPost)
	v1.HandleFunc("/webservices/verify", h.Verify).Methods(http.MethodGet)
	v1.HandleFunc(fmt.Sprintf("/webservices/{id:%s}", common.ObjectIDRegex), h.GetByID).Methods(http.MethodGet)
	v1.HandleFunc(fmt.Sprintf("/webservices/{id:%s}", common.ObjectIDRegex), h.Revoke).Methods(http.MethodDelete)
	v1.HandleFunc(fmt.Sprintf("/webservices/{id:%s}/extend", common.ObjectIDRegex), h.Extend).Methods(http.MethodPut)
	v1.HandleFunc("/webservices", h.GetAll).Methods(http.MethodGet)
}

func (h *WebserviceHandler) Create(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("WebserviceHandler", "create"))

	requestID := r.Header.Get("X-Request-ID")

	var body internal.CreateWebService

	// decode request body
	if err := riot.DecodeRequestBody(r, w, &body); err != nil {
		h.util.Logger.Error(err.Error())
		return
	}

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	webservice, err := h.svc.Create(r.Context(), body, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)

		return
	}

	riot.ResponseOK(w, webservice.ResponseData())
}

func (h *WebserviceHandler) GetByID(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("WebserviceHandler", "getByID"))

	requestID := r.Header.Get("X-Request-ID")

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	var includes []string
	id := mux.Vars(r)["id"] //nolint: gosimple
	includeQuery := r.URL.Query().Get("includes")

	if includeQuery != "" {
		includes = strings.Split(includeQuery, ",")
	}

	webservice, err := h.svc.GetByID(r.Context(), id, includes, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, webservice.ResponseData())
}

func (h *WebserviceHandler) GetAll(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("WebserviceHandler", "GetAll"))

	requestID := r.Header.Get("X-Request-ID")

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	urlValues := r.URL.Query()

	webservice, err := h.svc.GetAllWithPagination(r.Context(), urlValues, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, webservice)
}

func (h *WebserviceHandler) Revoke(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("WebserviceHandler", "Delete"))

	requestID := r.Header.Get("X-Request-ID")

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	// NOTE: Safe to ignore error, because it's always defined.
	id := mux.Vars(r)["id"] //nolint: gosimple

	isRevoke, err := h.svc.Revoke(r.Context(), id, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, isRevoke, "Data successfully revoke.")
}

func (h *WebserviceHandler) Update(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("WebserviceHandler", "Update"))

	requestID := r.Header.Get("X-Request-ID")

	// NOTE: Safe to ignore error, because it's always defined.
	id := mux.Vars(r)["id"] //nolint: gosimple

	var body internal.CreateWebService
	if err := riot.DecodeRequestBody(r, w, &body); err != nil {
		h.util.Logger.Error(err.Error())
		return
	}

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	webservice, err := h.svc.Update(r.Context(), id, body, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)

		return
	}

	riot.ResponseOK(w, webservice.ResponseData())
}

func (h *WebserviceHandler) Verify(w http.ResponseWriter, r *http.Request) {

	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("WebserviceHandler", "Verify"))

	requestId := r.Header.Get("X-Request-ID")

	token := r.URL.Query().Get("token")

	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	isVerify, err := h.svc.Verify(r.Context(), token, nexusData)

	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOKWithReqID(w, requestId, isVerify)
}

func (h *WebserviceHandler) Extend(w http.ResponseWriter, r *http.Request) {

	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("WebserviceHandler", "Extend"))

	requestId := r.Header.Get("X-Request-ID")

	id := mux.Vars(r)["id"]

	h.util.Logger.Info(id)

	riot.ResponseOKWithReqID(w, requestId, nil)
}
