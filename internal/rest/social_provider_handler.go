package rest

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strings"

	"github.com/continue-team/hecarim/internal"
	"github.com/continue-team/riot"
	common "github.com/continue-team/riot/pkg"

	"github.com/continue-team/riot/pkg/logger"
	"github.com/gorilla/mux"
	"go.uber.org/zap"
)

type SocialProviderSvc interface {
	Create(ctx context.Context, params internal.CreateSocialProvider, nexusData riot.Nexus) (_ internal.SocialProvider, err error)
	Update(ctx context.Context, id string, params internal.CreateSocialProvider, nexusData riot.Nexus) (_ internal.SocialProvider, err error)
	GetByID(ctx context.Context, id string, includes []string, nexusData riot.Nexus) (_ internal.SocialProvider, err error)
	Delete(ctx context.Context, id string, nexusData riot.Nexus) (bool, error)
	GetAllWithPagination(ctx context.Context, urlValues url.Values, nexusData riot.Nexus) (_ internal.SocialProviderPagin, err error)
}

type SocialProvidereHandler struct {
	svc  SocialProviderSvc
	util riot.Util
}

func NewSocialProvidereHandler(utl riot.Util, svc SocialProviderSvc) *SocialProvidereHandler {
	return &SocialProvidereHandler{
		svc:  svc,
		util: utl,
	}
}

// Register connects the handlers to the router.
func (h *SocialProvidereHandler) Register(r *mux.Router) {

	v1 := r.PathPrefix("/v1").Subrouter()

	v1.HandleFunc("/social-providers", h.Create).Methods(http.MethodPost)
	v1.HandleFunc(fmt.Sprintf("/social-providers/{id:%s}", common.ObjectIDRegex), h.GetByID).Methods(http.MethodGet)
	v1.HandleFunc(fmt.Sprintf("/social-providers/{id:%s}", common.ObjectIDRegex), h.Update).Methods(http.MethodPut)
	v1.HandleFunc(fmt.Sprintf("/social-providers/{id:%s}", common.ObjectIDRegex), h.Delete).Methods(http.MethodDelete)
	v1.HandleFunc("/social-providers", h.GetAll).Methods(http.MethodGet)
}

func (h *SocialProvidereHandler) Create(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("SocialProvidereHandler", "create"))

	requestID := r.Header.Get("X-Request-ID")

	var body internal.CreateSocialProvider

	// decode request body
	if err := riot.DecodeRequestBody(r, w, &body); err != nil {
		h.util.Logger.Error(err.Error())
		return
	}

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	socialProvider, err := h.svc.Create(r.Context(), body, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)

		return
	}

	riot.ResponseOK(w, socialProvider.ResponseData())
}

func (h *SocialProvidereHandler) GetByID(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("SocialProvidereHandler", "getByID"))

	requestID := r.Header.Get("X-Request-ID")

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	var includes []string
	id := mux.Vars(r)["id"] //nolint: gosimple
	includeQuery := r.URL.Query().Get("includes")

	if includeQuery != "" {
		includes = strings.Split(includeQuery, ",")
	}

	webservice, err := h.svc.GetByID(r.Context(), id, includes, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, webservice.ResponseData())
}

func (h *SocialProvidereHandler) GetAll(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("SocialProvidereHandler", "GetAll"))

	requestID := r.Header.Get("X-Request-ID")

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	urlValues := r.URL.Query()

	webservice, err := h.svc.GetAllWithPagination(r.Context(), urlValues, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, webservice)
}

func (h *SocialProvidereHandler) Delete(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("SocialProvidereHandler", "Delete"))

	requestID := r.Header.Get("X-Request-ID")

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	// NOTE: Safe to ignore error, because it's always defined.
	id := mux.Vars(r)["id"] //nolint: gosimple

	isRevoke, err := h.svc.Delete(r.Context(), id, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, isRevoke, "Data successfully revoke.")
}

func (h *SocialProvidereHandler) Update(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("SocialProvidereHandler", "Update"))

	requestID := r.Header.Get("X-Request-ID")

	// NOTE: Safe to ignore error, because it's always defined.
	id := mux.Vars(r)["id"] //nolint: gosimple

	var body internal.CreateSocialProvider
	if err := riot.DecodeRequestBody(r, w, &body); err != nil {
		h.util.Logger.Error(err.Error())
		return
	}

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	webservice, err := h.svc.Update(r.Context(), id, body, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)

		return
	}

	riot.ResponseOK(w, webservice.ResponseData())
}
