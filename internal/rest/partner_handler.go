package rest

import (
	"context"
	"fmt"
	"net/http"

	"github.com/continue-team/riot"

	"github.com/continue-team/riot/pkg/logger"
	"github.com/gorilla/mux"
	"go.uber.org/zap"
)

type PartnerSvc interface {
	Webhook(ctx context.Context, nexusData riot.Nexus) (interface{}, error)
}

type PartnerHandler struct {
	svc  PartnerSvc
	util riot.Util
}

func NewPartnerHandler(utl riot.Util, svc PartnerSvc) *PartnerHandler {
	return &PartnerHandler{
		svc:  svc,
		util: utl,
	}
}

// Register connects the handlers to the router.
func (h *PartnerHandler) Register(r *mux.Router) {

	v1 := r.PathPrefix("/partners").Subrouter()

	v1.HandleFunc(fmt.Sprintf("/webhooks/{provider:%s}", "[a-zA-Z0-9-]+"), h.Webhook).Methods(http.MethodGet)
}

func (h *PartnerHandler) Webhook(w http.ResponseWriter, r *http.Request) {
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("PartnerHandler", "Webhook"))

	h.util.Logger.Info("Webhook hit", zap.String("method", r.Method), zap.String("path", r.URL.Path), zap.Any("Query", r.URL.Query()))

	mode := r.URL.Query().Get("hub.mode")
	verifyToken := r.URL.Query().Get("hub.verify_token")
	challenge := r.URL.Query().Get("hub.challenge")

	h.util.Logger.Info("Received validation request", zap.String("mode", mode), zap.String("verifyToken", verifyToken), zap.String("challenge", challenge))

	if verifyToken == h.util.Conf.Get("INSTAGRAM_WEBHOOK_TOKEN") {
		h.util.Logger.Info("Webhook validated successfully")
		w.WriteHeader(http.StatusOK)
		_, _ = w.Write([]byte(challenge))
		return
	}

	h.util.Logger.Warn("Webhook validation failed")
	w.WriteHeader(http.StatusForbidden)
	_, _ = w.Write([]byte("Invalid verify token"))
}
