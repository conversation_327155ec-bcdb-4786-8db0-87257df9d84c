package rest

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strings"

	"github.com/continue-team/hecarim/internal"
	"github.com/continue-team/hecarim/pkg"
	"github.com/continue-team/riot"
	common "github.com/continue-team/riot/pkg"

	"github.com/continue-team/riot/pkg/logger"
	"github.com/gorilla/mux"
	"go.uber.org/zap"
)

type UserSvc interface {
	Create(ctx context.Context, identifier internal.Identifier, params internal.CreateUser, nexusData riot.Nexus) (_ internal.DataProfile, err error)
	Login(ctx context.Context, params internal.Login, nexusData riot.Nexus) (_ internal.DataProfile, err error)
	Delete(ctx context.Context, id string, nexusData riot.Nexus) (err error)
	Update(ctx context.Context, identifier internal.Identifier, id string, params internal.CreateUser, nexusData riot.Nexus) (_ internal.DataProfile, err error)
	GetByID(ctx context.Context, id string, includes []string, nexusData riot.Nexus) (_ internal.User, err error)
	GetAllWithPagination(ctx context.Context, urlValues url.Values, nexusData riot.Nexus) (_ internal.UserPagin, err error)
	Session(ctx context.Context, identifier internal.IdentifierSession, nexusData riot.Nexus) (_ internal.DataProfile, err error)
	Validate(ctx context.Context, validateType string, urlValues url.Values, nexusData riot.Nexus) (internal.ResponseValidate, error)
	UpdateCredential(ctx context.Context, typeCredential string, params internal.UpdateUserSecurity, nexusData riot.Nexus) (_ internal.DataProfile, err error)
	GetSessionByUserID(ctx context.Context, id string, includes []string, nexusData riot.Nexus) (_ internal.UserSession, err error)
	UpdateProfileSocial(ctx context.Context, identifier internal.Identifier, id string, params internal.UpdateUserSocialMedia, nexusData riot.Nexus) (_ internal.DataProfile, err error)
	GetByLoginID(ctx context.Context, loginID string, includes []string, nexusData riot.Nexus) (_ internal.User, err error)
}

type UserHandler struct {
	svc  UserSvc
	util riot.Util
}

func NewUserHandler(utl riot.Util, svc UserSvc) *UserHandler {
	return &UserHandler{
		svc:  svc,
		util: utl,
	}
}

// Register connects the handlers to the router.
func (h *UserHandler) Register(r *mux.Router) {

	v1 := r.PathPrefix("/v1").Subrouter()

	v1.HandleFunc("/profiles", h.Create).Methods(http.MethodPost)
	v1.HandleFunc("/profiles/login", h.Login).Methods(http.MethodPost)
	v1.HandleFunc("/profiles/security/password", h.UpdatePassword).Methods(http.MethodPut)
	v1.HandleFunc("/profiles/security/pin", h.UpdatePIN).Methods(http.MethodPut)
	v1.HandleFunc(fmt.Sprintf("/profiles/{id:%s}", common.ObjectIDRegex), h.GetByID).Methods(http.MethodGet)
	v1.HandleFunc(fmt.Sprintf("/profiles/loginid/{loginId:%s}", "[a-zA-Z0-9-]+"), h.GetByLoginID).Methods(http.MethodGet)
	v1.HandleFunc(fmt.Sprintf("/profiles/{id:%s}/sessions", common.ObjectIDRegex), h.GetSessionByUserID).Methods(http.MethodGet)
	v1.HandleFunc(fmt.Sprintf("/profiles/{id:%s}", common.ObjectIDRegex), h.Update).Methods(http.MethodPut)
	v1.HandleFunc(fmt.Sprintf("/profiles/{id:%s}/socials", common.ObjectIDRegex), h.UpdateProfileSocial).Methods(http.MethodPut)
	v1.HandleFunc("/profiles/sessions", h.Session).Methods(http.MethodGet)
	v1.HandleFunc(fmt.Sprintf("/profiles/validate/{type:%s}", "[a-zA-Z0-9-]+"), h.Validate).Methods(http.MethodGet)
	v1.HandleFunc("/profiles", h.GetAll).Methods(http.MethodGet)
}

func (h *UserHandler) Create(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("UserHandler", "create"))

	requestID := r.Header.Get("X-Request-ID")
	authorization := r.Header.Get("Authorization")

	var body internal.CreateUser

	// decode request body
	if err := riot.DecodeRequestBody(r, w, &body); err != nil {
		h.util.Logger.Error(err.Error())
		return
	}

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	//get ip
	ip := pkg.GetIP(r)
	body.CreatedIP = &ip

	user, err := h.svc.Create(r.Context(), internal.Identifier{
		AuthToken: authorization,
	}, body, nexusData)
	if err != nil {

		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)

		return
	}

	riot.ResponseOK(w, user.ResponseData())
}

func (h *UserHandler) Login(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("UserHandler", "Login"))

	requestID := r.Header.Get("X-Request-ID")

	var body internal.Login

	// decode request body
	if err := riot.DecodeRequestBody(r, w, &body); err != nil {
		h.util.Logger.Error(err.Error())
		return
	}

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	//get ip
	ip := pkg.GetIP(r)
	body.CreatedIP = ip

	user, err := h.svc.Login(r.Context(), body, nexusData)
	if err != nil {

		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)

		return
	}

	riot.ResponseOK(w, user.ResponseData())
}

func (h *UserHandler) UpdatePassword(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("UserHandler", "UpdatePassword"))

	requestID := r.Header.Get("X-Request-ID")

	var body internal.UpdateUserSecurity

	// decode request body
	if err := riot.DecodeRequestBody(r, w, &body); err != nil {
		h.util.Logger.Error(err.Error())
		return
	}

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	//get ip
	ip := pkg.GetIP(r)
	body.CreatedIP = &ip

	user, err := h.svc.UpdateCredential(r.Context(), "password", body, nexusData)
	if err != nil {

		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)

		return
	}

	riot.ResponseOK(w, user.ResponseData())
}

func (h *UserHandler) UpdatePIN(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("UserHandler", "UpdatePIN"))

	requestID := r.Header.Get("X-Request-ID")

	var body internal.UpdateUserSecurity

	// decode request body
	if err := riot.DecodeRequestBody(r, w, &body); err != nil {
		h.util.Logger.Error(err.Error())
		return
	}

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	//get ip
	ip := pkg.GetIP(r)
	body.CreatedIP = &ip

	user, err := h.svc.UpdateCredential(r.Context(), "pin", body, nexusData)
	if err != nil {

		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)

		return
	}

	riot.ResponseOK(w, user.ResponseData())
}

func (h *UserHandler) GetByID(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("UserHandler", "getByID"))

	requestID := r.Header.Get("X-Request-ID")

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	var includes []string
	id := mux.Vars(r)["id"] //nolint: gosimple
	includeQuery := r.URL.Query().Get("includes")

	if includeQuery != "" {
		includes = strings.Split(includeQuery, ",")
	}

	user, err := h.svc.GetByID(r.Context(), id, includes, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, user.ResponseData())
}

func (h *UserHandler) GetByLoginID(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("UserHandler", "GetByLoginID"))

	requestID := r.Header.Get("X-Request-ID")

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	var includes []string
	loginId := mux.Vars(r)["loginId"] //nolint: gosimple
	includeQuery := r.URL.Query().Get("includes")

	if includeQuery != "" {
		includes = strings.Split(includeQuery, ",")
	}

	user, err := h.svc.GetByLoginID(r.Context(), loginId, includes, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, user.ResponseData())
}

func (h *UserHandler) GetSessionByUserID(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("UserHandler", "GetSessionByUserID"))

	requestID := r.Header.Get("X-Request-ID")

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	var includes []string
	id := mux.Vars(r)["id"] //nolint: gosimple
	includeQuery := r.URL.Query().Get("includes")

	if includeQuery != "" {
		includes = strings.Split(includeQuery, ",")
	}

	user, err := h.svc.GetSessionByUserID(r.Context(), id, includes, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, user)
}

func (h *UserHandler) GetAll(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("UserHandler", "GetAll"))

	requestID := r.Header.Get("X-Request-ID")

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	urlValues := r.URL.Query()

	user, err := h.svc.GetAllWithPagination(r.Context(), urlValues, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, user)
}

func (h *UserHandler) Session(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("UserHandler", "Session"))

	requestID := r.Header.Get("X-Request-ID")

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	urlValues := r.URL.Query()
	session := urlValues.Get("session")
	identifier := internal.IdentifierSession{
		Session: session,
	}

	user, err := h.svc.Session(r.Context(), identifier, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, user.ResponseData())
}

func (h *UserHandler) Validate(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("UserHandler", "Validate"))

	requestID := r.Header.Get("X-Request-ID")

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	validateType := mux.Vars(r)["type"]
	urlValues := r.URL.Query()

	validate, err := h.svc.Validate(r.Context(), validateType, urlValues, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, validate)
}

func (h *UserHandler) Delete(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("UserHandler", "Delete"))

	requestID := r.Header.Get("X-Request-ID")

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	// NOTE: Safe to ignore error, because it's always defined.
	id := mux.Vars(r)["id"] //nolint: gosimple

	err := h.svc.Delete(r.Context(), id, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)
		return
	}

	riot.ResponseOK(w, nil, "Data successfully deleted.")
}

func (h *UserHandler) Update(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("UserHandler", "Update"))

	requestID := r.Header.Get("X-Request-ID")
	authorization := r.Header.Get("Authorization")

	// NOTE: Safe to ignore error, because it's always defined.
	id := mux.Vars(r)["id"] //nolint: gosimple

	var body internal.CreateUser
	if err := riot.DecodeRequestBody(r, w, &body); err != nil {
		h.util.Logger.Error(err.Error())
		return
	}

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	ip := pkg.GetIP(r)
	body.CreatedIP = &ip

	user, err := h.svc.Update(r.Context(), internal.Identifier{AuthToken: authorization}, id, body, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)

		return
	}

	riot.ResponseOK(w, user.ResponseData())
}

func (h *UserHandler) UpdateProfileSocial(w http.ResponseWriter, r *http.Request) {
	// define Logger
	h.util.Logger = logger.FromCtx(r.Context()).With(zap.String("UserHandler", "UpdateProfileSocial"))

	requestID := r.Header.Get("X-Request-ID")
	authorization := r.Header.Get("Authorization")

	// NOTE: Safe to ignore error, because it's always defined.
	id := mux.Vars(r)["id"] //nolint: gosimple

	var body internal.UpdateUserSocialMedia
	if err := riot.DecodeRequestBody(r, w, &body); err != nil {
		h.util.Logger.Error(err.Error())
		return
	}

	// Retrieve the Nexus value from the context
	nexusCtx := r.Context().Value(riot.RequestNexusCtxKey)
	nexusData, _ := nexusCtx.(riot.Nexus)

	if requestID == "" {
		r.Header.Set("X-Request-ID", nexusData.RequestId)
	}

	ip := pkg.GetIP(r)
	body.CreatedIP = &ip

	user, err := h.svc.UpdateProfileSocial(r.Context(), internal.Identifier{AuthToken: authorization}, id, body, nexusData)
	if err != nil {
		riot.RenderErrorResponse(r.Context(), w, err.Error(), err)

		return
	}

	riot.ResponseOK(w, user.ResponseData())
}
