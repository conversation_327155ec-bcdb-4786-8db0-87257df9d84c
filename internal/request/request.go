package request

import (
	"net/http"
	"strings"

	"github.com/carlm<PERSON><PERSON><PERSON>/requests"
	"github.com/continue-team/riot"
)

var DefaultValidator requests.ResponseHandler = requests.CheckStatus(
	http.StatusOK,
	http.StatusCreated,
	http.StatusAccepted,
	http.StatusNonAuthoritativeInfo,
	http.StatusNoContent,
	http.StatusBadRequest,
	http.StatusUnauthorized,
	http.StatusMethodNotAllowed,
)

func CommaStringToArray(array []string) string {
	return strings.Join(array, ",")
}

func ArrayToCommaString(array []string) string {
	return strings.Join(array, ",")
}

type Identifier struct {
	ClientKey    string
	ClientSecret string
	AccessToken  string
	Session      string
}

func (i Identifier) GetClientKey() string {
	return i.ClientKey
}

func (i Identifier) GetClientSecret() string {

	if i.AccessToken == "" {
		return ""
	}

	// Split the token into its parts
	parts := strings.Split(i.AccessToken, ".")
	if len(parts) == 0 {
		return ""
	}

	// Signature is the third part of the JWT
	secret := parts[len(parts)-1]

	return secret
}

func (i Identifier) GetAccessToken() string {
	return i.AccessToken
}

func (i Identifier) GetSession() string {
	return i.Session
}

type Request struct {
	baseURL string
	config  func(rb *requests.Builder)
	util    riot.Util
}

func NewRequest(util riot.Util, baseUrl string) *Request {

	config := func(rb *requests.Builder) {
		rb.AddValidator(DefaultValidator)
	}

	return &Request{
		baseURL: baseUrl,
		config:  config,
		util:    util,
	}
}
