package request

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/carlmjohnson/requests"
	"github.com/continue-team/hecarim/internal"
	"github.com/continue-team/riot"
	"go.uber.org/zap"
)

type FeeDetail struct {
	PaymentMethod            *string  `json:"payment_method,omitempty"`
	FeeType                  *string  `json:"fee_type,omitempty"`
	FeeValue                 *float64 `json:"fee_value,omitempty"`
	ProviderFeeType          *string  `json:"provider_fee_type,omitempty"`
	ProviderFeeValue         *float64 `json:"provider_fee_value,omitempty"`
	PlatformFeeType          *string  `json:"platform_fee_type,omitempty"`
	PlatformFeeValue         *float64 `json:"platform_fee_value,omitempty"`
	CashbackProviderFeeType  *string  `json:"cashback_provider_fee_type,omitempty"`
	CashbackProviderFeeValue *float64 `json:"cashback_provider_fee_value,omitempty"`
	ReferralFeeType          *string  `json:"referral_fee_type,omitempty"`
	ReferralFeeValue         *float64 `json:"referral_fee_value,omitempty"`
	MarketingFeeType         *string  `json:"marketing_fee_type,omitempty"`
	MarketingFeeValue        *float64 `json:"marketing_fee_value,omitempty"`
	Provider                 *string  `json:"provider,omitempty"`
}

type Voucher struct {
	ID               string      `json:"_id"`
	CompanyID        string      `json:"company_id"`
	Code             string      `json:"code"`
	Fees             []FeeDetail `json:"fees"`
	DiscountType     string      `json:"discount_type"`
	DiscountValue    float64     `json:"discount_value"`
	MinPurchase      float64     `json:"min_purchase"`
	MaxDiscount      float64     `json:"max_discount"`
	UsageLimit       int         `json:"usage_limit"`
	EnableLimit      bool        `json:"enable_limit"`
	RemainingUsage   int         `json:"remaining_usage"`
	ExpiresAt        *time.Time  `json:"expires_at"`
	ExpireType       string      `json:"expire_type"`
	ValidFrom        *time.Time  `json:"valid_from"`
	ValidTo          *time.Time  `json:"valid_to"`
	Period           int         `json:"period"`
	PeriodType       string      `json:"period_type"`
	LevelPriority    int         `json:"level_priority"`
	Type             string      `json:"type"`
	Combined         bool        `json:"combined"`
	CombinationTypes []string    `json:"combination_types"`
	Metadata         interface{} `json:"metadata"`
	Status           string      `json:"status"`
	CreatedAt        *time.Time  `json:"created_at"`
	UpdatedAt        *time.Time  `json:"updated_at"`
}

type ResponseVoucher struct {
	Code      int     `json:"code"`
	Data      Voucher `json:"data"`
	Messages  any     `json:"messages"`
	RequestID string  `json:"request_id"`
}

func (t *Request) ValidateVoucher(ctx context.Context, identifier Identifier, voucherCode string, query internal.QueryObject) (*ResponseVoucher, error) {
	if identifier.GetAccessToken() == "" {
		return nil, fmt.Errorf("client access token empty")
	}

	query.ByPass = "test"

	queries, _ := query.ToQueries(identifier.GetClientSecret())

	var response ResponseVoucher
	api := requests.New(t.config).
		BaseURL(t.baseURL).
		Path(fmt.Sprintf("/payment-gateway/v1/vouchers/code/%s", voucherCode)).
		Method(http.MethodGet).
		Params(queries)

	err := api.Header("Authorization", identifier.GetAccessToken()).
		ToJSON(&response).
		Fetch(ctx)

	if err != nil {
		return nil, riot.WrapErrorfLog(t.util.Logger, err, riot.ErrorCodeUnknown, "Request.Upload.api.ToJSON")
	}

	t.util.Logger.Info("Request.ValidateVoucher.Response", zap.Any("Response", response))

	return &response, nil
}
