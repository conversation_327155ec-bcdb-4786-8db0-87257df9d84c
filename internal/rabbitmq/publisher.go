package rabbitmq

import (
	"context"
	"fmt"
	"time"

	"github.com/continue-team/hecarim/internal"
	"github.com/continue-team/hecarim/internal/constant"
	"github.com/continue-team/riot"
	r "github.com/continue-team/riot/rabbitmq"
	"go.uber.org/zap"
)

type Publisher struct {
	util   riot.Util
	broker *r.Broker
}

func NewPublisher(util riot.Util, rmq *riot.RabbitMQ) (*Publisher, error) {

	broker, err := r.New<PERSON>roker(util, rmq)

	if err != nil {
		return nil, err
	}

	return &Publisher{
		util:   util,
		broker: broker,
	}, nil
}

func (t *Publisher) PublishCreateMerchant(ctx context.Context, payloadMerchant internal.PayloadCreateMerchant) error {

	const Exchange = "manaslu.exchange.backend"

	payload := r.PayloadEvent{
		Event:      constant.TaskEvtCreateMerchant,
		ID:         payloadMerchant.ProfileID,
		ActionDate: time.Now().UTC().String(),
		Data:       payloadMerchant,
	}

	t.util.Logger.Info(fmt.Sprintf("%s.%s", "publish", constant.TaskEvtCreateMerchant), zap.Any("payload", payload))

	return t.broker.PublishWithExchange(ctx, Exchange, "manaslu.route.backend", payload)
}
