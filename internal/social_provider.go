package internal

import (
	"fmt"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/pkg/util"
	validation "github.com/go-ozzo/ozzo-validation/v4"
)

type SocialProvider struct {
	ID              string     `json:"_id"`
	Profile         string     `json:"profile"`
	Name            string     `json:"name"`
	Description     string     `json:"description"`
	AuthKey         string     `json:"auth_key"`
	AuthSecret      string     `json:"auth_secret"`
	AuthCallback    string     `json:"auth_callback"`
	ConnectCallback string     `json:"connect_callback"`
	AuthScopes      string     `json:"auth_scopes"`
	Version         int        `json:"version"`
	Status          bool       `json:"status"`
	CreatedAt       *time.Time `json:"created_at"`
	UpdatedAt       *time.Time `json:"updated_at"`
}

type SocialProviderPagin struct {
	Limit        int              `json:"limit"`
	Page         int              `json:"page"`
	Sort         string           `json:"sort"`
	TotalRecords int              `json:"total_records"`
	TotalPages   int              `json:"total_pages"`
	Records      []SocialProvider `json:"records"`
}

func (f SocialProvider) ValidateExist() error {
	if f.ID != "" {
		return validation.Errors{
			util.GetStructName(f): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already taken.", f.Name)),
		}
	}
	return nil
}

func (f SocialProvider) ValidateNotExist() error {
	if f.ID == "" {
		return validation.Errors{
			util.GetStructName(f): riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", f.ID)),
		}
	}
	return nil
}

func (f *SocialProvider) ResponseData() interface{} {
	if f.ID == "" {
		return map[string]interface{}{}
	} else {
		return f
	}
}

type CreateSocialProvider struct {
	Profile         string  `json:"profile,omitempty"`
	Name            *string `json:"name,omitempty"`
	Description     *string `json:"description,omitempty"`
	AuthKey         *string `json:"auth_key,omitempty"`
	AuthSecret      *string `json:"auth_secret,omitempty"`
	AuthCallback    *string `json:"auth_callback,omitempty"`
	ConnectCallback *string `json:"connect_callback,omitempty"`
	AuthScopes      *string `json:"auth_scopes,omitempty"`
	Version         *int    `json:"version,omitempty"`
	Status          *bool   `json:"status,omitempty"`
}

func (c CreateSocialProvider) Validate() error {
	if err := validation.ValidateStruct(&c,
		validation.Field(&c.Name, validation.Required),
		validation.Field(&c.AuthKey, validation.Required),
		validation.Field(&c.AuthSecret, validation.Required),
		validation.Field(&c.AuthCallback, validation.Required),
		validation.Field(&c.Profile, validation.Required),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}

type QuerySocialProvider struct {
	Name string `json:"name,omitempty"`
}
