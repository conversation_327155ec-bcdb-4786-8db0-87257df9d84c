package firebaseauth

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"log"
	"net/http"

	"firebase.google.com/go/v4/auth"
)

type contextKey string

const FirebaseUserContextKey = contextKey("firebaseUser")
const FirebaseAuthHeader = "X-Auth-Firebase"

func AuthMiddleware(authClient *auth.Client) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()

			var buf bytes.Buffer
			tee := io.TeeReader(r.Body, &buf)
			defer r.Body.Close()

			var body struct {
				IDToken string `json:"id_token"`
			}
			if err := json.NewDecoder(tee).Decode(&body); err != nil || body.IDToken == "" {
				http.Error(w, "Missing or invalid id_token in body", http.StatusBadRequest)
				return
			}

			r.Body = io.NopCloser(&buf)

			token, err := authClient.VerifyIDToken(ctx, body.IDToken)
			if err != nil {
				log.Println("AuthMiddleware.VerifyIDToken: ", err)
				http.Error(w, "Invalid or expired ID Token", http.StatusUnauthorized)
				return
			}

			firebaseUser, err := ParseFirebaseJWT(token)
			if err != nil {
				http.Error(w, "Failed to parse ID Token claims", http.StatusInternalServerError)
				return
			}

			ctx = context.WithValue(ctx, FirebaseUserContextKey, firebaseUser)
			next.ServeHTTP(w, r.WithContext(ctx))
		})
	}
}

func GetFirebaseUserFromContext(ctx context.Context) (*FirebaseJWT, bool) {
	firebaseUser, ok := ctx.Value(FirebaseUserContextKey).(*FirebaseJWT)
	return firebaseUser, ok
}
