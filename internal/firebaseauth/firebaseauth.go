package firebaseauth

import (
	"context"

	firebase "firebase.google.com/go/v4"
	"firebase.google.com/go/v4/auth"
	"google.golang.org/api/option"
)

type FirebaseAuth struct {
	Client *firebase.App
}

func New(ctx context.Context, credentialsPath string) (*FirebaseAuth, error) {
	opt := option.WithCredentialsFile(credentialsPath)
	app, err := firebase.NewApp(ctx, nil, opt)
	if err != nil {
		return nil, err
	}

	return &FirebaseAuth{Client: app}, nil
}

func (f *FirebaseAuth) GetAuthClient(ctx context.Context) (*auth.Client, error) {
	return f.Client.Auth(ctx)
}
