package firebaseauth

import "regexp"

type FirebaseIdentities struct {
	Google []string `json:"google.com"`
	Email  []string `json:"email"`
}

type FirebaseClaims struct {
	Identities     FirebaseIdentities `json:"identities"`
	SignInProvider string             `json:"sign_in_provider"`
}

type FirebaseJWT struct {
	UID           string         `json:"uid"`
	Name          string         `json:"name"`
	Picture       string         `json:"picture"`
	Iss           string         `json:"iss"`
	Aud           string         `json:"aud"`
	AuthTime      int64          `json:"auth_time"`
	UserID        string         `json:"user_id"`
	Sub           string         `json:"sub"`
	Iat           int64          `json:"iat"`
	Exp           int64          `json:"exp"`
	Email         string         `json:"email"`
	EmailVerified bool           `json:"email_verified"`
	Firebase      FirebaseClaims `json:"firebase"`
}

func (u FirebaseJWT) RemoveRegexMail() string {
	if u.Email != "" {
		re := regexp.MustCompile(`@.*$`)
		return re.ReplaceAllString(u.Email, "")
	}
	return u.Email
}

// remove provider .com
func (u FirebaseJWT) RemoveRegexProvider() string {
	if u.Firebase.SignInProvider != "" {
		re := regexp.MustCompile(`@.*$`)
		return re.ReplaceAllString(u.Firebase.SignInProvider, "")
	}
	return u.Firebase.SignInProvider
}
