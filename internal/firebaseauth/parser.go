package firebaseauth

import (
	"encoding/json"

	"firebase.google.com/go/v4/auth"
)

func ParseFirebaseJWT(token *auth.Token) (*FirebaseJWT, error) {
	var firebaseJWT FirebaseJWT

	claimsJSON, err := json.Marshal(token.Claims)
	if err != nil {
		return nil, err
	}

	if err := json.Unmarshal(claimsJSON, &firebaseJWT); err != nil {
		return nil, err
	}

	firebaseJWT.UID = token.UID

	return &firebaseJWT, nil
}
