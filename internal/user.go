package internal

import (
	"errors"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"
	"unicode"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/pkg/util"
	validation "github.com/go-ozzo/ozzo-validation/v4"
	"github.com/go-ozzo/ozzo-validation/v4/is"
)

type DataProfile struct {
	Profile         User                  `json:"profile"`
	Session         Session               `json:"session"`
	Security        UserSecurityProtected `json:"security"`
	SocialProviders *[]UserSocialProvider `json:"social_providers,omitempty"`
}

func (f *DataProfile) ResponseData() interface{} {
	if f.Profile.ID == "" {
		return map[string]interface{}{}
	} else {
		return f
	}
}

type User struct {
	ID                  string     `json:"_id"`
	ParentRole          int        `json:"parent_role"`
	ParentID            string     `json:"parent_id"`
	ParentUsername      string     `json:"parent_username"`
	Role                int        `json:"role"`
	Username            string     `json:"username"`
	LoginID             string     `json:"login_id"`
	<PERSON><PERSON>               string     `json:"alias"`
	FirstName           string     `json:"first_name"`
	LastName            string     `json:"last_name"`
	NickName            string     `json:"nickname"`
	Email               string     `json:"email"`
	MobilePhone         string     `json:"mobile_phone"`
	MobilePhoneCountry  string     `json:"mobile_phone_country"`
	MobilePhoneCode     string     `json:"mobile_phone_code"`
	MobilePhoneNumber   string     `json:"mobile_phone_number"`
	MobilePhoneVerified bool       `json:"mobile_phone_verified"`
	Gender              int        `json:"gender"`
	Type                int        `json:"type"`
	Status              bool       `json:"status"`
	ParentBlocked       bool       `json:"parent_blocked"`
	CreatedIP           string     `json:"created_ip"`
	LastLoggedIn        *time.Time `json:"last_logged_in"`
	FirstTransaction    bool       `json:"first_transaction"`
	Group               string     `json:"group"`
	Avatar              string     `json:"avatar"`
	Bookmark            string     `json:"bookmark"`
	Bank                string     `json:"bank"`
	Mtc                 string     `json:"mtc"`
	AccountNumber       string     `json:"account_number"`
	AccountName         string     `json:"account_name"`
	LastLoggedInIP      string     `json:"last_logged_in_ip"`
	IsSocialMedia       bool       `json:"is_social_media"`
	TermsAccepted       bool       `json:"terms_accepted"`
	Referrer            string     `json:"referrer"`
	ReferrerCode        string     `json:"referrer_code"`
	ReferrerID          string     `json:"referrer_id"`
	MarketingCode       string     `json:"marketing_code"`
	MarketingID         string     `json:"marketing_id"`
	VoucherCode         string     `json:"voucher_code"`
	VoucherID           string     `json:"voucher_id"`
	CashbackID          string     `json:"cashback_id"`
	MerchantID          string     `json:"merchant_id"`
	CreatedAt           *time.Time `json:"created_at"`
	UpdatedAt           *time.Time `json:"updated_at"`
}

type UserPagin struct {
	Limit        int    `json:"limit"`
	Page         int    `json:"page"`
	Sort         string `json:"sort"`
	TotalRecords int    `json:"total_records"`
	TotalPages   int    `json:"total_pages"`
	Records      []User `json:"records"`
}

func (f User) ValidateExist() error {
	if f.ID != "" {
		return validation.Errors{
			util.GetStructName(f): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already taken.", f.LoginID)),
		}
	}
	return nil
}

func (f User) ValidatePhoneExist() error {
	if f.ID != "" {
		return validation.Errors{
			"mobile_phone": riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already taken.", f.MobilePhone)),
		}
	}
	return nil
}

func (f User) ValidateEmailExist() error {
	if f.ID != "" {
		return validation.Errors{
			"email": riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already taken.", f.Email)),
		}
	}
	return nil
}

func (f User) ValidateNotExist() error {
	if f.ID == "" {
		return validation.Errors{
			util.GetStructName(f): riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", f.ID)),
		}
	}
	return nil
}

func (f User) ValidateParentNotExist() error {
	if f.ID == "" {
		return validation.Errors{
			util.GetStructName(f): riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%sparent not exist.", f.ID)),
		}
	}
	return nil
}

func (f User) NotActive() error {
	if !f.Status {
		return validation.Errors{
			util.GetStructName(f): riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot active.", f.ID)),
		}
	}
	return nil
}

func (f User) CheckRole(webserviceRole int) error {

	if f.Role == riot.RoleCustomer && webserviceRole < riot.RoleCompany {
		return validation.Errors{
			"": riot.NewErrorf(riot.ErrorCodeNotFound, "Profile not found."),
		}
	}
	return nil
}

func (f User) IsValidParent(parent riot.NexusProfile, direct bool) bool {

	if !direct && parent.Role == 1 {
		return true
	}

	if f.ID == parent.ID {
		return true
	}

	if f.ParentID != "" && f.ParentID == parent.ID {
		return true
	}

	if f.Role < parent.Role {
		return false
	}

	return false
}

func (f *User) ResponseData() interface{} {
	if f.ID == "" {
		return map[string]interface{}{}
	} else {
		return f
	}
}

type CreateUser struct {
	Company             string     `json:"company,omitempty"`
	Password            string     `json:"password,omitempty"`
	Pin                 string     `json:"pin,omitempty"`
	ParentRole          *int       `json:"parent_role,omitempty"`
	ParentID            string     `json:"parent_id,omitempty"`
	ParentUsername      *string    `json:"parent_username,omitempty"`
	Role                *int       `json:"role,omitempty"`
	Username            *string    `json:"username,omitempty"`
	LoginID             *string    `json:"login_id,omitempty"`
	Alias               *string    `json:"alias,omitempty"`
	FirstName           *string    `json:"first_name,omitempty"`
	LastName            *string    `json:"last_name,omitempty"`
	NickName            *string    `json:"nickname,omitempty"`
	Email               *string    `json:"email,omitempty"`
	MobilePhone         *string    `json:"mobile_phone,omitempty"`
	MobilePhoneCountry  *string    `json:"mobile_phone_country,omitempty"`
	MobilePhoneCode     *string    `json:"mobile_phone_code,omitempty"`
	MobilePhoneNumber   *string    `json:"mobile_phone_number,omitempty"`
	MobilePhoneVerified *bool      `json:"mobile_phone_verified,omitempty"`
	Gender              *int       `json:"gender,omitempty"`
	Type                *int       `json:"type,omitempty"`
	Status              *bool      `json:"status,omitempty"`
	ParentBlocked       *bool      `json:"parent_blocked,omitempty"`
	CreatedIP           *string    `json:"created_ip,omitempty"`
	LastLoggedIn        *time.Time `json:"last_logged_in,omitempty"`
	FirstTransaction    *bool      `json:"first_transaction,omitempty"`
	Group               *string    `json:"group,omitempty"`
	Avatar              *string    `json:"avatar,omitempty"`
	Bookmark            *string    `json:"bookmark,omitempty"`
	Bank                *string    `json:"bank,omitempty"`
	Mtc                 *string    `json:"mtc,omitempty"`
	AccountNumber       *string    `json:"account_number,omitempty"`
	AccountName         *string    `json:"account_name,omitempty"`
	LastLoggedInIP      *string    `json:"last_logged_in_ip,omitempty"`
	IsSocialMedia       *bool      `json:"is_social_media,omitempty"`
	TermsAccepted       *bool      `json:"terms_accepted,omitempty"`
	Referrer            *string    `json:"referrer,omitempty"`
	ReferrerCode        *string    `json:"referrer_code,omitempty"`
	ReferrerID          *string    `json:"referrer_id,omitempty"`
	MarketingID         *string    `json:"marketing_id,omitempty"`
	MarketingCode       *string    `json:"marketing_code,omitempty"`
	VoucherID           *string    `json:"voucher_id,omitempty"`
	VoucherCode         *string    `json:"voucher_code,omitempty"`
	CashbackID          *string    `json:"cashback_id,omitempty"`
	Cashback            *string    `json:"cashback,omitempty"`
	CreateMerchant      *bool      `json:"create_merchant,omitempty"`
	MerchantID          *string    `json:"merchant_id,omitempty"`
	IsGroup             bool       `json:"is_group,omitempty"`
}

func validateForbiddenWords(forbiddenWords []string) validation.RuleFunc {
	return func(value interface{}) error {
		var username string

		if strPtr, ok := value.(*string); ok && strPtr != nil {
			username = strings.ToLower(*strPtr)
		} else if str, ok := value.(string); ok {
			username = strings.ToLower(str)
		} else {
			return validation.NewError("validation_invalid_type", "Input must be a string or *string.")
		}

		fmt.Printf("Processed username: '%s'\n", username)

		for _, word := range forbiddenWords {
			word = strings.ToLower(strings.TrimSpace(word))
			fmt.Printf("Checking if '%s' contains '%s': %t\n", username, word, strings.Contains(username, word))

			if strings.Contains(username, word) {
				return validation.NewError(
					"validation_forbidden_word",
					fmt.Sprintf("must not contain forbidden word: '%s'", word),
				)
			}
		}

		return nil
	}
}

var forbiddenWords = []string{
	"login", "register", "event", "news", "product", "partner",
	"about", "carrier", "responsibility", "privacy", "term",
	"tos", "faq", "adm", "admin", "bot", "prv", "guest", "trial",
}

func (c CreateUser) Validate() error {
	if err := validation.ValidateStruct(&c,
		validation.Field(&c.ParentID, validation.Required, is.MongoID),
		validation.Field(&c.Username, validation.Required, validation.Length(6, 15)),
		validation.Field(&c.LoginID, validation.Required, validation.By(validateForbiddenWords(forbiddenWords))),
		validation.Field(&c.Email, is.Email),
		validation.Field(&c.Password, validation.Required, validation.Length(8, 128)),
		validation.Field(&c.MobilePhone, validation.Required, is.Int),
		// validation.Field(&c.Alias, validation.By(validateAlias)),
		// validation.Field(&c.FirstName, validation.By(validateGeneral("^[a-zA-Z0-9.\\s]+$"))),
		// validation.Field(&c.LastName, validation.By(validateGeneral("^[a-zA-Z0-9.\\s]+$"))),
		// validation.Field(&c.NickName, validation.By(validateGeneral("^[a-zA-Z0-9.\\s]+$"))),
		// validation.Field(&c.Avatar, validation.By(validateGeneral("^[a-zA-Z0-9.\\s]+$"))),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}

func (c CreateUser) ValidateSocialMedia() error {
	if err := validation.ValidateStruct(&c,
		validation.Field(&c.ParentID, validation.Required, is.MongoID),
		validation.Field(&c.Username, validation.Required, validation.Length(6, 15)),
		validation.Field(&c.LoginID, validation.Required, validation.By(validateForbiddenWords(forbiddenWords))),
		validation.Field(&c.Email, is.Email),
		// validation.Field(&c.Alias, validation.By(validateAlias)),
		// validation.Field(&c.FirstName, validation.By(validateGeneral("^[a-zA-Z0-9.\\s]+$"))),
		// validation.Field(&c.LastName, validation.By(validateGeneral("^[a-zA-Z0-9.\\s]+$"))),
		// validation.Field(&c.NickName, validation.By(validateGeneral("^[a-zA-Z0-9.\\s]+$"))),
		// validation.Field(&c.Avatar, validation.By(validateGeneral("^[a-zA-Z0-9.\\s]+$"))),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}

func (c CreateUser) ValidateVoucher(statusCode int) error {
	if statusCode != http.StatusOK {
		return validation.Errors{
			"Voucher": riot.NewErrorf(riot.ErrorCodeNotFound, "not found."),
		}
	}

	return nil
}

func (c CreateUser) ValidateUsernameLength(role int, parentUsername string, validReferrer bool) error {

	length := len(*c.Username)
	username := c.Username
	var errMessage string

	if length > 16 {
		return validation.Errors{
			util.GetStructName(c): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s max username is 16 chars.", *username)),
		}
	}

	if length != 1 && role == 3 {
		errMessage = "need to be 1 characters."
	} else if length != 2 && role == 4 {
		errMessage = "need to be 2 characters."
	} else if length != 3 && role == 5 {
		errMessage = "need to be 3 characters."
	} else if length < 6 && role == 6 {
		errMessage = "need to be 6 characters."
	} else if length < 6 && role == 7 && validReferrer {
		errMessage = "should have 6 or more characters."
	} else if length < 6 && role == 7 && !validReferrer {
		errMessage = "should have 6 or more characters."
	}

	// if role < riot.RoleCustomer && role > riot.RoleCorporateAdm {
	// 	if parentUsername == "" {
	// 		errMessage = "Wrong Input: parentUsername is required."
	// 	}

	// 	if !strings.HasPrefix(*c.Username, parentUsername) {
	// 		errMessage = "Invalid username: must start with parentUsername."
	// 	}
	// }

	if errMessage != "" {
		return validation.Errors{
			"Username": riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s %s", *username, errMessage)),
		}
	}

	return nil
}

type QueryUser struct {
	Name string `json:"name,omitempty"`
}

func validateAlias(value interface{}) error {
	alias, _ := value.(string)
	// Transform alias to uppercase and remove non-alphanumeric characters
	cleanedAlias := strings.ToUpper(removeNonAlphanumeric(alias))
	if len(cleanedAlias) == 0 {
		return errors.New("alias must contain alphanumeric characters")
	}
	return nil
}

func removeNonAlphanumeric(s string) string {
	var cleaned strings.Builder
	for _, r := range s {
		if unicode.IsLetter(r) || unicode.IsDigit(r) {
			cleaned.WriteRune(r)
		}
	}
	return cleaned.String()
}

func filterUsername(username string) string {
	// Apply regex to remove unwanted characters, except alphanumeric and '@'
	reg := regexp.MustCompile(`[^a-zA-Z0-9@]`)
	cleanedUsername := reg.ReplaceAllString(username, "")
	return strings.ToUpper(cleanedUsername)
}

func validateUsername(value interface{}) error {
	username, _ := value.(string)
	cleanedUsername := filterUsername(username)
	if len(cleanedUsername) == 0 {
		return errors.New("Username must contain valid characters")
	}
	return nil
}

// filterGeneral applies regex to remove unwanted characters and transforms to uppercase
func filterGeneral(input string, allowedPattern string) string {
	// Remove characters that are not allowed based on the provided regex pattern
	reg := regexp.MustCompile(allowedPattern)
	cleanedInput := reg.ReplaceAllString(input, "")
	// Convert to uppercase
	return strings.ToUpper(cleanedInput)
}

// validateGeneral is a generalized validation function for any field
func validateGeneral(allowedPattern string) validation.RuleFunc {
	return func(value interface{}) error {
		input, _ := value.(string)
		// Use the passed allowedPattern to filter the input
		cleanedInput := filterGeneral(input, allowedPattern)
		if len(cleanedInput) == 0 {
			return errors.New("input must contain valid characters")
		}
		return nil
	}
}

type ParentProfile struct {
	ParentID string `json:"parent_id,omitempty"`
	ID       string `json:"_id,omitempty"`
	Username string `json:"username"`
	LoginID  string `json:"login_id,omitempty"`
	Role     int    `json:"role,omitempty"`
}

func (f ParentProfile) NotAllowByRole() error {

	if f.Role > 6 {
		return validation.Errors{
			"role": riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%d not allow.", f.Role)),
		}
	}
	return nil
}

type Login struct {
	Username  string `json:"username,omitempty"`
	Password  string `json:"password,omitempty"`
	Force     bool   `json:"force,omitempty"`
	CreatedIP string `json:"created_ip,omitempty"`
	Forever   bool   `json:"forever,omitempty"`
}

func (c Login) Validate() error {
	if err := validation.ValidateStruct(&c,
		validation.Field(&c.Username, validation.Required),
		validation.Field(&c.Password, validation.Required),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}

type ResponseValidate struct {
	Validate bool `json:"validate"`
}

type PayloadCreateMerchant struct {
	Username    string `json:"username,omitempty"`
	LoginID     string `json:"login_id,omitempty"`
	CompanyID   string `json:"company_id,omitempty"`
	ProfileID   string `json:"profile_id,omitempty"`
	ReferrerID  string `json:"referrer_id,omitempty"`
	MarketingID string `json:"marketing_id,omitempty"`
	VoucherID   string `json:"voucher_id,omitempty"`
}

type Identifier struct {
	UserID        string `json:"user_id"`
	Period        string `json:"period"`
	State         string `json:"state"`
	IntegrationID string `json:"integration_id"`
	AuthToken     string `json:"auth_token"`
}

func (i *Identifier) Validate() error {
	if i.UserID == "" {
		return validation.Errors{
			util.GetStructName(i): riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", i.UserID)),
		}
	}
	return nil
}

func (i *Identifier) ValidateState() error {
	if i.State == "" {
		return validation.Errors{
			"state": riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", i.State)),
		}
	}
	return nil
}

func (i *Identifier) ValidatePeriod() error {
	if i.Period == "" {
		return validation.Errors{
			"period": riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", i.Period)),
		}
	}
	return nil
}

func (i *Identifier) ValidateIntegrationId() error {
	if i.IntegrationID == "" {
		return validation.Errors{
			"integration_id": riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", i.IntegrationID)),
		}
	}
	return nil
}

type UserSession struct {
	Session string `json:"session"`
}

type UpdateUserSocialMedia struct {
	Password            string  `json:"password,omitempty"`
	Pin                 string  `json:"pin,omitempty"`
	Username            *string `json:"username,omitempty"`
	LoginID             *string `json:"login_id,omitempty"`
	FirstName           *string `json:"first_name,omitempty"`
	Avatar              *string `json:"avatar,omitempty"`
	LastName            *string `json:"last_name,omitempty"`
	NickName            *string `json:"nickname,omitempty"`
	Email               *string `json:"email,omitempty"`
	MobilePhone         *string `json:"mobile_phone,omitempty"`
	MobilePhoneCountry  *string `json:"mobile_phone_country,omitempty"`
	MobilePhoneCode     *string `json:"mobile_phone_code,omitempty"`
	MobilePhoneNumber   *string `json:"mobile_phone_number,omitempty"`
	MobilePhoneVerified *bool   `json:"mobile_phone_verified,omitempty"`
	Gender              *int    `json:"gender,omitempty"`
	CreatedIP           *string `json:"created_ip,omitempty"`
	IsSocialMedia       *bool   `json:"is_social_media,omitempty"`
	ReferrerCode        *string `json:"referrer_code,omitempty"`
	ReferrerID          *string `json:"referrer_id,omitempty"`
	MarketingID         *string `json:"marketing_id,omitempty"`
	MarketingCode       *string `json:"marketing_code,omitempty"`
	VoucherID           *string `json:"voucher_id,omitempty"`
	VoucherCode         *string `json:"voucher_code,omitempty"`
	CashbackID          *string `json:"cashback_id,omitempty"`
	Cashback            *string `json:"cashback,omitempty"`
	CreateMerchant      *bool   `json:"create_merchant,omitempty"`
	MerchantID          *string `json:"merchant_id,omitempty"`
	TermsAccepted       *bool   `json:"terms_accepted,omitempty"`
}

func (c UpdateUserSocialMedia) Validate() error {
	if err := validation.ValidateStruct(&c,
		validation.Field(&c.Username, validation.Required, validation.Length(6, 15)),
		validation.Field(&c.LoginID, validation.Required, validation.By(validateForbiddenWords(forbiddenWords))),
		// validation.Field(&c.Email, is.Email),
		validation.Field(&c.Username, validation.Required),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}

type PayloadUpdateMerchant struct {
	CompanyID  string `json:"company_id,omitempty"`
	ProfileID  string `json:"profile_di,omitempty"`
	MerchantID string `json:"merchant_id,omitempty"`
}
