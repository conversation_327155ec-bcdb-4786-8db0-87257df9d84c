package internal

import (
	"fmt"
	"net/http"
	"time"

	"github.com/continue-team/riot"
	"github.com/continue-team/riot/pkg/util"
	validation "github.com/go-ozzo/ozzo-validation/v4"
)

type UserSecurity struct {
	ID            string     `json:"_id"`
	User          string     `json:"user"`
	LoginID       string     `json:"login_id"`
	<PERSON>as         string     `json:"alias"`
	ResetLoginID  bool       `json:"reset_login_id"`
	ResetPassword bool       `json:"reset_password"`
	Password      string     `json:"password"`
	Pin           string     `json:"pin"`
	ResetPin      bool       `json:"reset_pin"`
	WhitelistIP   string     `json:"whitelist_ip"`
	CreatedIP     string     `json:"created_ip"`
	CreatedAt     *time.Time `json:"created_at"`
	UpdatedAt     *time.Time `json:"updated_at"`
}

type UserSecurityProtected struct {
	ID            string     `json:"_id"`
	User          string     `json:"user"`
	LoginID       string     `json:"login_id"`
	<PERSON><PERSON>         string     `json:"alias"`
	ResetLoginID  bool       `json:"reset_login_id"`
	ResetPassword bool       `json:"reset_password"`
	Pin           string     `json:"pin"`
	ResetPin      bool       `json:"reset_pin"`
	WhitelistIP   string     `json:"whitelist_ip"`
	CreatedIP     string     `json:"created_ip"`
	CreatedAt     *time.Time `json:"created_at"`
	UpdatedAt     *time.Time `json:"updated_at"`
}

type UserSecurityPagin struct {
	Limit        int            `json:"limit"`
	Page         int            `json:"page"`
	Sort         string         `json:"sort"`
	TotalRecords int            `json:"total_records"`
	TotalPages   int            `json:"total_pages"`
	Records      []UserSecurity `json:"records"`
}

func (f UserSecurity) ValidateExist() error {
	if f.ID != "" {
		return validation.Errors{
			util.GetStructName(f): riot.NewErrorf(riot.ErrorCodeInvalidArgument, fmt.Sprintf("%s is already taken.", f.Alias)),
		}
	}
	return nil
}

func (f UserSecurity) CheckPassword(password, decryptPassword string) error {
	if password != decryptPassword {
		return validation.Errors{
			"": riot.NewErrorf(riot.ErrorCodeInvalidArgument, "Wrong password."),
		}
	}
	return nil
}

func (f UserSecurity) CheckPin(oldPIN string) error {
	if f.Pin != oldPIN {
		return validation.Errors{
			"": riot.NewErrorf(riot.ErrorCodeInvalidArgument, "Wrong PIN."),
		}
	}
	return nil
}

func (f UserSecurity) ValidateNotExist() error {
	if f.ID == "" {
		return validation.Errors{
			util.GetStructName(f): riot.NewErrorf(riot.ErrorCodeNotFound, fmt.Sprintf("%snot exist.", f.ID)),
		}
	}
	return nil
}

func (f *UserSecurity) ResponseData() interface{} {
	if f.ID == "" {
		return map[string]interface{}{}
	} else {
		return f
	}
}

type CreateUserSecurity struct {
	User          string  `json:"user,omitempty"`
	LoginID       *string `json:"login_id,omitempty"`
	Alias         *string `json:"alias,omitempty"`
	ResetLoginID  *bool   `json:"reset_login_id,omitempty"`
	Password      *string `json:"password,omitempty"`
	ResetPassword *bool   `json:"reset_password,omitempty"`
	Pin           *string `json:"pin,omitempty"`
	ResetPin      *bool   `json:"reset_pin,omitempty"`
	WhitelistIP   *string `json:"whitelist_ip,omitempty"`
	CreatedIP     *string `json:"created_ip,omitempty"`
}

func (c CreateUserSecurity) Validate() error {
	if err := validation.ValidateStruct(&c,
		validation.Field(&c.User, validation.Required),
		validation.Field(&c.Password, validation.Required),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}

type QueryUserSecurity struct {
	Name string `json:"name,omitempty"`
}

type UpdateUserSecurity struct {
	User        string  `json:"user,omitempty"`
	Downline    *string `json:"downline,omitempty"`
	Force       *bool   `json:"force,omitempty"`
	Password    *string `json:"password,omitempty"`
	OldPassword *string `json:"old_password,omitempty"`
	Pin         *string `json:"pin,omitempty"`
	OldPin      *string `json:"old_pin,omitempty"`
	CreatedIP   *string `json:"created_ip,omitempty"`
}

func (c UpdateUserSecurity) Validate(typeUpdate string) error {

	switch typeUpdate {
	case "password":
		if err := validation.ValidateStruct(&c,
			validation.Field(&c.User, validation.Required),
			validation.Field(&c.Password, validation.Required),
		); err != nil {
			return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
		}

	case "pin":
		if err := validation.ValidateStruct(&c,
			validation.Field(&c.User, validation.Required),
			validation.Field(&c.Pin, validation.Required),
		); err != nil {
			return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
		}
	}

	return nil
}

type AuthIdentifier struct {
	State         string `json:"state,omitempty"`
	Code          string `json:"code,omitempty"`
	OauthVerifier string `json:"oauth_verifier,omitempty"`
	OauthToken    string `json:"oauth_token,omitempty"`
	Domain        string `json:"domain,omitempty"`
	IsGroup       string `json:"is_group,omitempty"`
	Req           *http.Request
	Res           http.ResponseWriter
}

func (c AuthIdentifier) Validate() error {

	if err := validation.ValidateStruct(&c,
		validation.Field(&c.State, validation.Required),
	); err != nil {
		return riot.WrapErrorf(err, riot.ErrorCodeInvalidArgument, "Required")
	}

	return nil
}
