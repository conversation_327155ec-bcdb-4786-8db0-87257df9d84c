package models

import (
	"github.com/continue-team/riot/mongorm"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type SocialProvider struct {
	mongorm.Model   `bson:",inline"`
	Profile         primitive.ObjectID `bson:"profile,omitempty"`
	Name            *string            `bson:"name,omitempty"`
	Description     *string            `bson:"description,omitempty"`
	AuthKey         *string            `bson:"auth_key,omitempty"`
	AuthSecret      *string            `bson:"auth_secret,omitempty"`
	AuthCallback    *string            `bson:"auth_callback,omitempty"`
	ConnectCallback *string            `bson:"connect_callback,omitempty"`
	AuthScopes      *string            `bson:"auth_scopes,omitempty"`
	Version         *int               `bson:"version,omitempty"`
	Status          *bool              `bson:"status,omitempty"`
}

const SocialProviderCollection = "social_providers"
