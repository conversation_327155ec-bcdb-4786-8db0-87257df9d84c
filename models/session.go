package models

import (
	"time"

	"github.com/continue-team/riot/mongorm"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Session struct {
	mongorm.Model `bson:",inline"`
	Parent        primitive.ObjectID `bson:"parent,omitempty"`
	User          primitive.ObjectID `bson:"user,omitempty"`
	Username      *string            `bson:"username,omitempty"`
	LoginID       *string            `bson:"login_id,omitempty"`
	Session       *string            `bson:"session,omitempty"`
	Guest         *bool              `bson:"guest,omitempty"`
	Online        *bool              `bson:"online,omitempty"`
	HistoricalMtc *string            `bson:"historical_mtc,omitempty"`
	RecentMtc     *string            `bson:"recent_mtc,omitempty"`
	CreatedIP     *string            `bson:"created_ip,omitempty"`
	LastAct       *time.Time         `bson:"last_act,omitempty"`
	ValidUntil    *time.Time         `bson:"valid_until,omitempty"`
}

const SessionCollection = "sessions"
