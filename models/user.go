package models

import (
	"time"

	"github.com/continue-team/riot/mongorm"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type User struct {
	mongorm.Model       `bson:",inline"`
	Company             primitive.ObjectID  `bson:"company,omitempty"`
	ParentRole          *int                `bson:"parent_role,omitempty"`
	ParentID            primitive.ObjectID  `bson:"parent_id,omitempty"`
	ParentUsername      *string             `bson:"parent_username,omitempty"`
	Role                *int                `bson:"role,omitempty"`
	Username            *string             `bson:"username,omitempty"`
	LoginID             *string             `bson:"login_id,omitempty"`
	Alias               *string             `bson:"alias,omitempty"`
	FirstName           *string             `bson:"first_name,omitempty"`
	LastName            *string             `bson:"last_name,omitempty"`
	NickName            *string             `bson:"nickname,omitempty"`
	Email               *string             `bson:"email,omitempty"`
	MobilePhone         *string             `bson:"mobile_phone,omitempty"`
	MobilePhoneCountry  *string             `bson:"mobile_phone_country,omitempty"`
	MobilePhoneCode     *string             `bson:"mobile_phone_code,omitempty"`
	MobilePhoneNumber   *string             `bson:"mobile_phone_number,omitempty"`
	MobilePhoneVerified *bool               `bson:"mobile_phone_verified,omitempty"`
	Gender              *int                `bson:"gender,omitempty"`
	Type                *int                `bson:"type,omitempty"`
	Status              *bool               `bson:"status,omitempty"`
	ParentBlocked       *bool               `bson:"parent_blocked,omitempty"`
	CreatedIP           *string             `bson:"created_ip,omitempty"`
	LastLoggedIn        *time.Time          `bson:"last_logged_in,omitempty"`
	FirstTransaction    *bool               `bson:"first_transaction,omitempty"`
	Group               *string             `bson:"group,omitempty"`
	Avatar              *string             `bson:"avatar,omitempty"`
	Bookmark            *string             `bson:"bookmark,omitempty"`
	Bank                *string             `bson:"bank,omitempty"`
	Mtc                 *string             `bson:"mtc,omitempty"`
	AccountNumber       *string             `bson:"account_number,omitempty"`
	AccountName         *string             `bson:"account_name,omitempty"`
	LastLoggedInIP      *string             `bson:"last_logged_in_ip,omitempty"`
	IsSocialMedia       *bool               `bson:"is_social_media,omitempty"`
	TermsAccepted       *bool               `bson:"terms_accepted,omitempty"`
	Referrer            *string             `bson:"referrer,omitempty"`
	ReferrerCode        *string             `bson:"referrer_code,omitempty"`
	ReferrerID          *primitive.ObjectID `bson:"referrer_id,omitempty"`
	MarketingCode       *string             `bson:"marketing_code,omitempty"`
	MarketingID         *primitive.ObjectID `bson:"marketing_id,omitempty"`
	VoucherCode         *string             `bson:"voucher_code,omitempty"`
	VoucherID           *primitive.ObjectID `bson:"voucher_id,omitempty"`
	MerchantID          *primitive.ObjectID `bson:"merchant_id,omitempty"`
}

const UserCollection = "users"
