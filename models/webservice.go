package models

import (
	"time"

	"github.com/continue-team/riot/mongorm"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type WebService struct {
	mongorm.Model `bson:",inline"`
	Profile       primitive.ObjectID `bson:"profile,omitempty"`
	Name          *string            `bson:"name,omitempty"`
	Description   *string            `bson:"description,omitempty"`
	AppID         *string            `bson:"app_id,omitempty"`
	AppSecret     *string            `bson:"app_secret,omitempty"`
	Version       *int               `bson:"version,omitempty"`
	Telegram      *bool              `bson:"telegram,omitempty"`
	Status        *bool              `bson:"status,omitempty"`
	ValidUntil    *time.Time         `bson:"valid_until,omitempty"`
}

const WebServiceCollection = "webservices"
