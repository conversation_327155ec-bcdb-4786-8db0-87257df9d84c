package models

import (
	"github.com/continue-team/riot/mongorm"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type UserSocialProvider struct {
	mongorm.Model `bson:",inline"`
	User          primitive.ObjectID `bson:"user,omitempty"`
	Parent        primitive.ObjectID `bson:"parent,omitempty"`
	LoginID       *string            `bson:"login_id,omitempty"`
	Alias         *string            `bson:"alias,omitempty"`
	Provider      *string            `bson:"provider,omitempty"`
	Email         *string            `bson:"email,omitempty"`
	Name          *string            `bson:"name,omitempty"`
	FirstName     *string            `bson:"firstname,omitempty"`
	LastName      *string            `bson:"lastname,omitempty"`
	NickName      *string            `bson:"nickname,omitempty"`
	Description   *string            `bson:"description,omitempty"`
	UserID        *string            `bson:"user_id,omitempty"`
	AvatarURL     *string            `bson:"avatar_url,omitempty"`
	Location      *string            `bson:"location,omitempty"`
	WhitelistIP   *string            `bson:"whitelist_ip,omitempty"`
	CreatedIP     *string            `bson:"created_ip,omitempty"`
}

const UserSocialProviderCollection = "user_social_providers"
