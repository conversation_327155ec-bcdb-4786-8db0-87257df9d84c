package models

import (
	"time"

	"github.com/continue-team/riot/mongorm"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ReferrerCode struct {
	mongorm.Model `bson:",inline"`
	Profile       primitive.ObjectID `bson:"profile,omitempty"`
	Code          *string            `bson:"code,omitempty"`
	Description   *string            `bson:"description,omitempty"`
	Status        *bool              `bson:"status,omitempty"`
	ValidUntil    *time.Time         `bson:"valid_until,omitempty"`
}

const ReferrerCodeCollection = "referrer_codes"
