package models

import (
	"github.com/continue-team/riot/mongorm"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type UserSecurity struct {
	mongorm.Model `bson:",inline"`
	User          primitive.ObjectID `bson:"user,omitempty"`
	LoginID       *string            `bson:"login_id,omitempty"`
	Alias         *string            `bson:"alias,omitempty"`
	ResetLoginID  *bool              `bson:"reset_login_id,omitempty"`
	Password      *string            `bson:"password,omitempty"`
	ResetPassword *bool              `bson:"reset_password,omitempty"`
	Pin           *string            `bson:"pin,omitempty"`
	ResetPin      *bool              `bson:"reset_pin,omitempty"`
	WhitelistIP   *string            `bson:"whitelist_ip,omitempty"`
	CreatedIP     *string            `bson:"created_ip,omitempty"`
}

const UserSecurityCollection = "user_securities"
