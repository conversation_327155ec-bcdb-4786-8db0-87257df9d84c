FROM golang:1.22.5-alpine3.20 AS builder

# Install git
RUN apk update && apk add --no-cache git

# Accept build arguments for GitHub token
ARG GITHUB_TOKEN

RUN apk --no-cache add git && \
    rm -rf /var/cache/apk/*

# Configure git to use the GitHub token for authentication

RUN git config --global url."https://${GITHUB_TOKEN}@github.com/".insteadOf "https://github.com/"

# Set environment variables
ENV GOPRIVATE=github.com/continue-team/*

RUN apk --no-cache add \
      alpine-sdk \
      librdkafka-dev \
      pkgconf && \
    rm -rf /var/cache/apk/*

WORKDIR /build/

COPY . .

#RUN go mod tidy && go mod vendor
RUN go get github.com/continue-team/riot@latest
RUN go mod download

# Remove Git token configuration for security

RUN git config --global --unset-all url."https://${GITHUB_TOKEN}@github.com/".insteadOf

RUN CGO_ENABLED=1 GOOS=linux go build -a -installsuffix cgo -ldflags "-extldflags -static" -tags musl \
      github.com/continue-team/hecarim/cmd/rest-server
#-

# FROM alpine:3.13 AS certificates

# RUN apk --no-cache add ca-certificates

#-

# FROM scratch

# WORKDIR /api/
# ENV PATH=/api/bin/:$PATH

# COPY --from=certificates /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/ca-certificates.crt

# COPY --from=builder /build/rest-server ./bin/rest-server
# COPY --from=builder /build/.env.example .
# #COPY ./bin/rest-server ./rest-server
COPY .env.example .env

EXPOSE 8086

CMD ["./rest-server", "--env", ".env", "--address", ":8086"]