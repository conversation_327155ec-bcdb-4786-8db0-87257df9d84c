# "HECARIM"

Project Structure

Talking specifically about microservices **only**, the structure I like to recommend is the following, everything using `<` and `>` depends on the domain being implemented and the bounded context being defined.

- [ ] `docker/`: defines the code used for creating infrastructure as well as docker containers.
- [ ] `config/`: defines the code used for configurations, example `cloudflare`, `mongodb`, `jenkins` and `rabbitmq`
  - [ ] `<executableN>/`: contains a Dockerfile used for building the binary.
- [ ] `cmd/`
  - [ ] `<rest-server>/`: main rest-api
  - [ ] `<rabbitmq>/`: handle subscriber rabbitmq
- [ ] `models/`: contains definitions and implementations of data models used within the application
- [ ] `internal/`: defines the _core domain_.
  - [ ] `<datastoreN>/`: a concrete _repository_ used by the domain, for example `mongodb`
  - [ ] `http/`: defines HTTP Handlers.
  - [ ] `service/`: orchestrates use cases and manages transactions.
- [ ] `pkg/` public API meant to be imported by other Go package.

There are cases where requiring a new bounded context is needed, in those cases the recommendation would be to
define a package like `internal/<bounded-context>` that then should follow the same structure, for example:

- `internal/<bounded-context>/`
  - `internal/<bounded-context>/<datastoreN>`
  - `internal/<bounded-context>/http`
  - `internal/<bounded-context>/service`

## Prerequisites

Make sure you have the following tools installed before proceeding:

1. [direnv](https://direnv.net/)
2. Docker

## Setup

### Step 1: Configure direnv

If you haven't already installed `direnv`, you can install it by following the instructions on [direnv's official website](https://direnv.net/).

If you encounter any permission issues, run the following command:

```bash
direnv allow .
```

### Step 2: Start Docker

Ensure that Docker is up and running on your system.

### Step 3: Start the project

Navigate to the project directory and run the following command to start the project:

```bash
docker-compose up -f docker-compose-dev.yaml up -d
```

This command will set up the project using the development environment configuration.

### Step 4:  Run the Application

To run the application, navigate to the cmd/<`app`> directory and execute the following command:

```bash
go run main.go --address :8080 --env ../../.env
```
