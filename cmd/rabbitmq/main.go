package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/continue-team/hecarim/config"
	"github.com/continue-team/hecarim/internal/constant"
	"github.com/continue-team/hecarim/internal/repositories"
	"github.com/continue-team/hecarim/internal/tasks"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/pkg/logger"
	r "github.com/continue-team/riot/rabbitmq"
	amqp "github.com/rabbitmq/amqp091-go"
	"go.uber.org/zap"
)

func main() {
	var env, exchange, queue, consumer string
	flag.StringVar(&env, "env", ".env", "Environment Variables filename")
	flag.StringVar(&exchange, "exchange", "hecarim.exchange.backend", "Type Exchange")
	flag.StringVar(&queue, "queue", "hecarim.queue", "Type Queue")
	flag.StringVar(&consumer, "consumer", "hecarim", "Consumer")
	flag.Parse()

	errC, err := run(env, exchange, queue, consumer)
	if err != nil {
		log.Fatalf("Couldn't run: %s", err)
	}

	if err := <-errC; err != nil {
		log.Fatalf("Error while running: %s", err)
	}
}

func run(env, exchange, queue, consumer string) (<-chan error, error) {
	l := logger.Get()

	if err := riot.Load(env); err != nil {
		return nil, riot.WrapErrorfLog(l, err, riot.ErrorCodeUnknown, "config.envvar.Load")
	}

	conf := riot.NewConfig()

	rmq, err := config.NewRabbitMQ(conf, l)
	if err != nil {
		return nil, riot.WrapErrorfLog(l, err, riot.ErrorCodeUnknown, "config.rabbitmq.NewRabbitMQ")
	}

	mongoDB, err := config.NewMongoSQL(conf)

	if err != nil {
		return nil, riot.WrapErrorfLog(l, err, riot.ErrorCodeUnknown, "config.gorm.NewMongoSQL")
	}

	util := riot.Util{
		Logger: l,
		DB:     mongoDB.DB,
		Conf:   conf,
	}

	subs := r.NewSubscriber(rmq)

	userRepo := repositories.NewUserRepository(util)

	Task := tasks.NewTask(
		util,
		userRepo,
	)

	srv := &Server{
		Exchange:   exchange,
		Queue:      queue,
		Consumer:   consumer,
		Logger:     l,
		Task:       Task,
		RabbitMQ:   rmq,
		Conf:       conf,
		Subscriber: subs,
		done:       make(chan struct{}),
	}

	errC := make(chan error, 1)

	ctx, stop := signal.NotifyContext(context.Background(),
		os.Interrupt,
		syscall.SIGTERM,
		syscall.SIGQUIT,
	)

	go func() {
		<-ctx.Done()

		l.Info("Shutdown signal received")

		ctxTimeout, cancel := context.WithTimeout(context.Background(), 10*time.Second)

		defer func() {
			_ = l.Sync()

			rmq.Close()
			stop()
			cancel()
			close(errC)
		}()

		if err := srv.Shutdown(ctxTimeout); err != nil {
			errC <- err
		}

		l.Info("Shutdown completed")
	}()

	go func() {
		l.Info("Listening and serving")

		if err := srv.ListenAndServe(); err != nil {
			errC <- err
		}
	}()

	return errC, nil
}

type Server struct {
	Exchange   string
	Queue      string
	Consumer   string
	Mongo      *riot.MongoDB
	Conf       *riot.Configuration
	RabbitMQ   *riot.RabbitMQ
	Task       *tasks.Task
	Subscriber *r.Subscriber
	Logger     *zap.Logger
	done       chan struct{}
}

func (s *Server) ListenAndServe() error {

	// Consume QueueBind direct method
	// This consumer script subscribes to messages without any specific routing key pattern, i.e., it consumes messages directly.
	// Unlike in topic exchanges where routing keys are used for pattern matching, direct exchanges deliver messages based on a
	// one-to-one matching.

	if err := s.subscribeAndProcess(s.Exchange, "hecarim.route.backend", s.Queue, s.Consumer); err != nil {
		return err
	}

	return nil
}

// Shutdown ...
func (s *Server) Shutdown(ctx context.Context) error {
	s.Logger.Info("Shutting down server")

	_ = s.RabbitMQ.Channel.Cancel(s.Consumer, true)

	for {
		select {
		case <-ctx.Done():
			return riot.WrapErrorfLog(s.Logger, ctx.Err(), riot.ErrorCodeUnknown, "context.Done")
		case <-s.done:
			return nil
		}
	}
}

func (s *Server) processMessages(msgs <-chan amqp.Delivery, routingKey string) {
	go func() {
		for msg := range msgs {
			var nack bool
			var evt r.PayloadEvent

			// Unmarshal the JSON data into the struct
			if err := json.Unmarshal(msg.Body, &evt); err != nil {
				s.Logger.Error(fmt.Sprintf("Error decoding JSON: %v", err))
				nack = true
			} else {

				s.Logger.Info(fmt.Sprintf("Event:%s Received message: %v", evt.Event, zap.Any("Data", evt.Data)))

				switch evt.Event {
				case constant.TaskEvtUpdateMerchant:
					nack = s.Subscriber.HandleEvent(s.Task.TaskEvtUpdateMerchant, constant.TaskEvtUpdateMerchant, evt)

				default:
					nack = false
				}
			}

			if nack {
				s.Logger.Info("NAcking :(")
				_ = msg.Nack(false, nack)
			} else {
				s.Logger.Info("Acking :)")
				_ = msg.Ack(false)
			}
		}

		s.Logger.Info(fmt.Sprintf("No more messages to consume for routing key: %s. Exiting.", routingKey))
		s.done <- struct{}{}
	}()
}

func (s *Server) subscribeAndProcess(exchange, routingKey, queue, consumer string) error {

	s.Logger.Info(fmt.Sprintf("Start: Exchange:%s Queue:%s Consumer:%s RoutingKey:%s", exchange, queue, consumer, routingKey))

	msgs, err := s.Subscriber.RunWithReconnect(exchange, routingKey, queue, consumer)
	if err != nil {
		return riot.WrapErrorfLog(s.Logger, err, riot.ErrorCodeUnknown, "Subscriber.Run.Routing")
	}

	s.processMessages(msgs, routingKey)
	return nil
}
