package main

import (
	"context"
	"errors"
	"flag"
	"log"
	"net/http"
	"os"
	"os/signal"
	"strconv"
	"syscall"
	"time"

	"firebase.google.com/go/v4/auth"
	"github.com/continue-team/hecarim/config"
	"github.com/continue-team/hecarim/internal"
	"github.com/continue-team/hecarim/internal/rabbitmq"
	"github.com/continue-team/hecarim/internal/repositories"
	"github.com/continue-team/hecarim/internal/rest"
	"github.com/continue-team/hecarim/internal/services"
	"github.com/continue-team/riot"
	riotMidd "github.com/continue-team/riot/middlewares"
	"github.com/continue-team/riot/pkg/logger"
	"github.com/didip/tollbooth/v6"
	"github.com/didip/tollbooth/v6/limiter"
	"github.com/go-redis/redis/v8"
	"github.com/gorilla/mux"
	"go.uber.org/zap"
)

func main() {
	var env, address string

	flag.StringVar(&env, "env", ".env", "Environment Variables filename")
	flag.StringVar(&address, "address", ":8086", "HTTP Server Address")
	flag.Parse()

	errC, err := run(env, address)
	if err != nil {
		log.Fatalf("Couldn't run: %s", err)
	}

	if err := <-errC; err != nil {
		log.Fatalf("Error while running: %s", err)
	}
}

// run starts the server
func run(env, address string) (<-chan error, error) {

	l := logger.Get()

	if err := riot.Load(env); err != nil {
		return nil, riot.WrapErrorfLog(l, err, riot.ErrorCodeNotFound, "config.envvar.Load")
	}

	conf := riot.NewConfig()

	mongoDB, err := config.NewMongoSQL(conf)

	if err != nil {
		return nil, riot.WrapErrorfLog(l, err, riot.ErrorCodeUnknown, "config.gorm.NewMongoSQL")
	}

	redis, err := config.NewRedis(conf)

	if err != nil {
		return nil, riot.WrapErrorfLog(l, err, riot.ErrorCodeUnknown, "config.gorm.NewRedis")
	}

	rmq, err := config.NewRabbitMQ(conf, l)
	if err != nil {
		return nil, riot.WrapErrorfLog(l, err, riot.ErrorCodeUnknown, "config.rabbitmq.NewRabbitMQ")
	}

	ctx := context.Background()

	fbAuth, err := config.NewFirebaseAuth(ctx, conf)

	if err != nil {
		return nil, riot.WrapErrorfLog(l, err, riot.ErrorCodeUnknown, "config.rabbitmq.NewFirebaseAuth")
	}

	authInstance, err := fbAuth.GetAuthClient(ctx)

	if err != nil {
		return nil, riot.WrapErrorfLog(l, err, riot.ErrorCodeUnknown, "fbAuth.GetAuthClient")
	}

	srv, err := newServer(serverConfig{
		FBAuth:  authInstance,
		Address: address,
		Mongo:   mongoDB,
		Logger:  l,
		Middlewares: []mux.MiddlewareFunc{
			(&riotMidd.MiddlewareNexus{Logger: l, Conf: conf}).Middleware(),
			(&riotMidd.RequestLogger{Logger: l, Conf: conf}).Middleware(),
			(&riotMidd.InterceptorReqID{Logger: l, Conf: conf}).Middleware(),
		},
		Conf:     conf,
		Redis:    redis,
		RabbitMQ: rmq,
	})

	if err != nil {
		return nil, riot.WrapErrorfLog(l, err, riot.ErrorCodeUnknown, "newServer")
	}

	errC := make(chan error, 1)

	ctx, stop := signal.NotifyContext(context.Background(),
		os.Interrupt,
		syscall.SIGTERM,
		syscall.SIGQUIT)

	go func() {
		<-ctx.Done()

		l.Info("Shutdown signal received")

		ctxTimeout, cancel := context.WithTimeout(context.Background(), 5*time.Second)

		defer func() {
			_ = mongoDB.Client.Disconnect(context.Background())
			_ = redis.Close()
			_ = rmq.Connection.Close()
			_ = l.Sync()
			stop()
			cancel()
			close(errC)
		}()

		srv.SetKeepAlivesEnabled(false)

		if err := srv.Shutdown(ctxTimeout); err != nil {
			errC <- err
		}
		l.Info("Shutdown Complete")
	}()

	go func() {
		l.Info("Listening and serving", zap.String("address", address))

		// "ListenAndServe always returns a non-nil error. After Shutdown or Close, the returned error is
		// ErrServerClosed."
		if err := srv.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			errC <- err
		}
	}()

	return errC, nil
}

type serverConfig struct {
	Address     string
	Mongo       *riot.MongoDB
	Middlewares []mux.MiddlewareFunc
	Logger      *zap.Logger
	Conf        *riot.Configuration
	Redis       *redis.Client
	RabbitMQ    *riot.RabbitMQ
	FBAuth      *auth.Client
}

func newServer(conf serverConfig) (*http.Server, error) {
	util := riot.Util{
		Logger: conf.Logger,
		DB:     conf.Mongo.DB,
		Conf:   conf.Conf,
		Redis:  conf.Redis,
	}

	redisCache := internal.NewRedis(util)

	router := mux.NewRouter()
	excludeMiddlewareRouter := mux.NewRouter()

	for _, mw := range conf.Middlewares {
		router.Use(mw)
	}

	taskPublisher, err := rabbitmq.NewPublisher(util, conf.RabbitMQ)

	if err != nil {
		return nil, err
	}

	// repositories
	webserviceRepo := repositories.NewWebserviceRepository(util)
	userRepo := repositories.NewUserRepository(util)
	userSecurityRepo := repositories.NewUserSecurityRepository(util)
	userSocialProviderRepo := repositories.NewUserSocialProviderRepository(util)
	sessionRepo := repositories.NewSessionRepository(util)
	referrerCodeRepo := repositories.NewReferrerCodeRepository(util)
	socialProviderRepo := repositories.NewSocialProviderRepository(util)

	// services
	referrerCodeSvc := services.NewReferrerCodeService(util, referrerCodeRepo)
	webserviceSvc := services.NewWebserviceService(util, webserviceRepo, userRepo, redisCache)
	userSecuritySvc := services.NewUserSecurityService(util, userSecurityRepo)
	sessionSvc := services.NewSessionService(util, sessionRepo, redisCache)
	userSvc := services.NewUserService(util, userRepo, sessionRepo, userSocialProviderRepo, userSecuritySvc, sessionSvc, referrerCodeSvc, taskPublisher)
	authSocialSvc := services.NewAuthSocialService(util, userRepo, userSocialProviderRepo, socialProviderRepo, userSvc, sessionSvc, redisCache)
	socialProviderSvc := services.NewSocialProviderService(util, socialProviderRepo, redisCache)
	partnerSvc := services.NewPartnerService(util)

	// routes
	rest.NewWebserviceHandler(util, webserviceSvc).Register(router)
	rest.NewUserHandler(util, userSvc).Register(router)
	rest.NewReferrerCodeHandler(util, referrerCodeSvc).Register(router)
	rest.NewAuthSocialHandler(util, authSocialSvc, conf.FBAuth).Register(router)
	rest.NewSocialProvidereHandler(util, socialProviderSvc).Register(router)
	rest.NewPartnerHandler(util, partnerSvc).Register(excludeMiddlewareRouter)

	limitValue, _ := strconv.ParseFloat(util.Conf.Get("RATE_LIMIT"), 64)

	// This could be the rate limit, indicating that the associated operation is allowed to occur up to 3 times within a specified time window.
	lmt := tollbooth.NewLimiter(float64(limitValue), &limiter.ExpirableOptions{DefaultExpirationTTL: time.Second})

	lmtmw := tollbooth.LimitHandler(lmt, router)
	// Merge the routers
	mainRouter := mux.NewRouter()
	mainRouter.PathPrefix("/v1").Handler(lmtmw)
	mainRouter.PathPrefix("/partners").Handler(excludeMiddlewareRouter)

	return &http.Server{
		Handler:           mainRouter,
		Addr:              conf.Address,
		ReadTimeout:       10 * time.Second,
		ReadHeaderTimeout: 10 * time.Second,
		WriteTimeout:      10 * time.Second,
		IdleTimeout:       10 * time.Second,
	}, nil
}
