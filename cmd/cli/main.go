package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"

	"github.com/continue-team/hecarim/config"
	"github.com/continue-team/hecarim/internal"
	"github.com/continue-team/hecarim/internal/cmd"
	"github.com/continue-team/hecarim/internal/repositories"
	"github.com/continue-team/hecarim/internal/services"
	"github.com/continue-team/riot"
	"github.com/continue-team/riot/pkg/logger"
	"github.com/go-redis/redis/v8"
	"github.com/spf13/cobra"
	"go.uber.org/zap"
)

func main() {
	var env string

	flag.StringVar(&env, "env", ".env", "Environment Variables filename")
	flag.Parse()

	errC, err := run(env)
	if err != nil {
		log.Fatalf("Couldn't run: %s", err)
	}

	if err := <-errC; err != nil {
		log.Fatalf("Error while running: %s", err)
	}
}

// run starts the server
func run(env string) (<-chan error, error) {

	l := logger.Get()

	if err := riot.Load(env); err != nil {
		return nil, riot.WrapErrorfLog(l, err, riot.ErrorCodeNotFound, "config.envvar.Load")
	}

	conf := riot.NewConfig()

	mongoDB, err := config.NewMongoSQL(conf)

	if err != nil {
		return nil, riot.WrapErrorfLog(l, err, riot.ErrorCodeUnknown, "config.gorm.NewMongoSQL")
	}

	redis, err := config.NewRedis(conf)

	if err != nil {
		return nil, riot.WrapErrorfLog(l, err, riot.ErrorCodeUnknown, "config.gorm.NewRedis")
	}

	rmq, err := config.NewRabbitMQ(conf, l)
	if err != nil {
		return nil, riot.WrapErrorfLog(l, err, riot.ErrorCodeUnknown, "config.rabbitmq.NewRabbitMQ")
	}

	cmd, err := newServer(serverConfig{
		Mongo:    mongoDB,
		Logger:   l,
		Conf:     conf,
		Redis:    redis,
		RabbitMQ: rmq,
	})

	if err != nil {
		return nil, riot.WrapErrorfLog(l, err, riot.ErrorCodeUnknown, "newServer")
	}

	errC := make(chan error, 1)

	ctx, stop := signal.NotifyContext(context.Background(),
		os.Interrupt,
		syscall.SIGTERM,
		syscall.SIGQUIT)

	go func() {
		<-ctx.Done()

		l.Info("Shutdown signal received")

		defer func() {
			_ = mongoDB.Client.Disconnect(context.Background())
			_ = redis.Close()
			_ = rmq.Connection.Close()
			_ = l.Sync()
			stop()
			close(errC)
		}()

		l.Info("Shutdown Complete")
	}()

	if err := cmd.Execute(); err != nil {
		fmt.Println("🚨 CLI execution error:", err)
		os.Exit(1)
	}

	return errC, nil
}

type serverConfig struct {
	Address  string
	Mongo    *riot.MongoDB
	Logger   *zap.Logger
	Conf     *riot.Configuration
	Redis    *redis.Client
	RabbitMQ *riot.RabbitMQ
}

func newServer(conf serverConfig) (*cobra.Command, error) {
	util := riot.Util{
		Logger: conf.Logger,
		DB:     conf.Mongo.DB,
		Conf:   conf.Conf,
		Redis:  conf.Redis,
	}

	redisCache := internal.NewRedis(util)

	// repositories
	webserviceRepo := repositories.NewWebserviceRepository(util)
	userRepo := repositories.NewUserRepository(util)

	// services
	webserviceSvc := services.NewWebserviceService(util, webserviceRepo, userRepo, redisCache)

	// Buat root command
	rootCmd := &cobra.Command{
		Use:   "cli",
		Short: "MyCLI is a sample command-line application",
		Long:  "A simple CLI application built with Go and Cobra",
	}

	// Tambahkan subcommands
	webserviceCmd := cmd.WebServiceCommand(webserviceSvc)
	fmt.Println("✅ Registering command:", webserviceCmd.Use) // Debugging

	rootCmd.AddCommand(webserviceCmd)

	return rootCmd, nil
}
